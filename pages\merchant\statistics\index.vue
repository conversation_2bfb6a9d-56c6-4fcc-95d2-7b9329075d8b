<template>
	<view class="statistics">
		<!-- 时间筛选 -->
		<view class="time-filter">
			<view class="filter-tabs">
				<view class="tab-item" :class="{ active: timeRange === 'today' }" @click="setTimeRange('today')">
					今日
				</view>
				<view class="tab-item" :class="{ active: timeRange === 'week' }" @click="setTimeRange('week')">
					本周
				</view>
				<view class="tab-item" :class="{ active: timeRange === 'month' }" @click="setTimeRange('month')">
					本月
				</view>
				<view class="tab-item" :class="{ active: timeRange === 'custom' }" @click="setTimeRange('custom')">
					自定义
				</view>
			</view>
			
			<!-- 自定义时间选择 -->
			<view class="custom-time" v-if="timeRange === 'custom'">
				<picker mode="date" :value="startDate" @change="onStartDateChange">
					<view class="date-picker">
						<text>{{ startDate || '开始日期' }}</text>
						<text class="iconfont icon-riqi"></text>
					</view>
				</picker>
				<text class="date-separator">至</text>
				<picker mode="date" :value="endDate" @change="onEndDateChange">
					<view class="date-picker">
						<text>{{ endDate || '结束日期' }}</text>
						<text class="iconfont icon-riqi"></text>
					</view>
				</picker>
			</view>
		</view>

		<!-- 营业额统计 -->
		<view class="revenue-stats">
			<view class="stats-title">营业额统计</view>
			<view class="revenue-overview">
				<view class="revenue-item">
					<view class="revenue-amount">¥{{ revenueData.total || '0.00' }}</view>
					<view class="revenue-label">总营业额</view>
				</view>
				<view class="revenue-item">
					<view class="revenue-amount">¥{{ revenueData.average || '0.00' }}</view>
					<view class="revenue-label">日均营业额</view>
				</view>
			</view>
			
			<!-- 营业额趋势图 -->
			<view class="chart-container">
				<view class="chart-placeholder" v-if="!revenueChart.length">
					<text>暂无数据</text>
				</view>
				<view class="simple-chart" v-else>
					<view class="chart-bar" v-for="(item, index) in revenueChart" :key="index">
						<view class="bar" :style="{ height: getBarHeight(item.amount, revenueData.max) + '%' }"></view>
						<view class="bar-label">{{ item.date }}</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 销售统计 -->
		<view class="sales-stats">
			<view class="stats-title">销售统计</view>
			<view class="sales-overview">
				<view class="sales-grid">
					<view class="sales-item">
						<view class="sales-number">{{ salesData.totalOrders || 0 }}</view>
						<view class="sales-label">总订单数</view>
					</view>
					<view class="sales-item">
						<view class="sales-number">{{ salesData.totalGoods || 0 }}</view>
						<view class="sales-label">商品销量</view>
					</view>
					<view class="sales-item">
						<view class="sales-number">{{ salesData.newCustomers || 0 }}</view>
						<view class="sales-label">新客户数</view>
					</view>
					<view class="sales-item">
						<view class="sales-number">{{ salesData.repeatRate || '0%' }}</view>
						<view class="sales-label">复购率</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 热销商品 -->
		<view class="hot-goods">
			<view class="stats-title">热销商品 TOP 5</view>
			<view class="goods-list">
				<view class="goods-item" v-for="(goods, index) in hotGoods" :key="goods.id">
					<view class="goods-rank">{{ index + 1 }}</view>
					<view class="goods-image">
						<image :src="goods.image" mode="aspectFill"></image>
					</view>
					<view class="goods-info">
						<view class="goods-name">{{ goods.name }}</view>
						<view class="goods-sales">销量：{{ goods.sales }}</view>
					</view>
					<view class="goods-revenue">¥{{ goods.revenue }}</view>
				</view>
			</view>
		</view>

		<!-- 订单状态统计 -->
		<view class="order-stats">
			<view class="stats-title">订单状态统计</view>
			<view class="order-overview">
				<view class="order-item">
					<view class="order-count">{{ orderStats.pending || 0 }}</view>
					<view class="order-label">待付款</view>
				</view>
				<view class="order-item">
					<view class="order-count">{{ orderStats.processing || 0 }}</view>
					<view class="order-label">待发货</view>
				</view>
				<view class="order-item">
					<view class="order-count">{{ orderStats.shipping || 0 }}</view>
					<view class="order-label">待收货</view>
				</view>
				<view class="order-item">
					<view class="order-count">{{ orderStats.completed || 0 }}</view>
					<view class="order-label">已完成</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { getRevenueStatistics, getSalesStatistics, getOrderStatistics } from '@/api/merchant_manage.js';

export default {
	data() {
		return {
			timeRange: 'today',
			startDate: '',
			endDate: '',
			revenueData: {},
			revenueChart: [],
			salesData: {},
			hotGoods: [],
			orderStats: {},
			loading: false
		}
	},
	onLoad() {
		this.loadStatistics();
	},
	methods: {
		// 设置时间范围
		setTimeRange(range) {
			this.timeRange = range;
			if (range !== 'custom') {
				this.startDate = '';
				this.endDate = '';
			}
			this.loadStatistics();
		},

		// 开始日期选择
		onStartDateChange(e) {
			this.startDate = e.detail.value;
			if (this.endDate) {
				this.loadStatistics();
			}
		},

		// 结束日期选择
		onEndDateChange(e) {
			this.endDate = e.detail.value;
			if (this.startDate) {
				this.loadStatistics();
			}
		},

		// 获取时间参数
		getTimeParams() {
			const params = {
				range: this.timeRange
			};
			
			if (this.timeRange === 'custom' && this.startDate && this.endDate) {
				params.start_date = this.startDate;
				params.end_date = this.endDate;
			}
			
			return params;
		},

		// 加载统计数据
		async loadStatistics() {
			if (this.loading) return;
			
			this.loading = true;
			const params = this.getTimeParams();

			try {
				const [revenueRes, salesRes, orderRes] = await Promise.all([
					getRevenueStatistics(params),
					getSalesStatistics(params),
					getOrderStatistics(params)
				]);

				this.revenueData = revenueRes.data.overview || {};
				this.revenueChart = revenueRes.data.chart || [];
				this.salesData = salesRes.data.overview || {};
				this.hotGoods = salesRes.data.hotGoods || [];
				this.orderStats = orderRes.data || {};

			} catch (error) {
				this.$util.Tips({
					title: error.message || '获取统计数据失败'
				});
			} finally {
				this.loading = false;
			}
		},

		// 计算柱状图高度
		getBarHeight(amount, max) {
			if (!max || max === 0) return 0;
			return Math.max((amount / max) * 100, 5);
		}
	}
}
</script>

<style lang="scss" scoped>
.statistics {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 40rpx;
}

.time-filter {
	background: white;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.filter-tabs {
	display: flex;
	background: #f5f5f5;
	border-radius: 50rpx;
	padding: 8rpx;
}

.tab-item {
	flex: 1;
	text-align: center;
	padding: 16rpx 0;
	border-radius: 50rpx;
	font-size: 28rpx;
	color: #666;
	transition: all 0.3s;
}

.tab-item.active {
	background: var(--view-theme);
	color: white;
}

.custom-time {
	display: flex;
	align-items: center;
	margin-top: 30rpx;
	gap: 20rpx;
}

.date-picker {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx;
	border: 1rpx solid #e0e0e0;
	border-radius: 8rpx;
	font-size: 28rpx;
}

.date-separator {
	font-size: 28rpx;
	color: #666;
}

.revenue-stats,
.sales-stats,
.hot-goods,
.order-stats {
	background: white;
	margin-bottom: 20rpx;
	padding: 30rpx;
}

.stats-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
}

.revenue-overview {
	display: flex;
	justify-content: space-around;
	margin-bottom: 40rpx;
}

.revenue-item {
	text-align: center;
}

.revenue-amount {
	font-size: 48rpx;
	font-weight: bold;
	color: var(--view-theme);
	margin-bottom: 8rpx;
}

.revenue-label {
	font-size: 26rpx;
	color: #666;
}

.chart-container {
	height: 300rpx;
	border: 1rpx solid #f0f0f0;
	border-radius: 8rpx;
	padding: 20rpx;
}

.chart-placeholder {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 100%;
	color: #999;
	font-size: 28rpx;
}

.simple-chart {
	display: flex;
	align-items: end;
	height: 100%;
	gap: 10rpx;
}

.chart-bar {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	height: 100%;
}

.bar {
	width: 100%;
	background: linear-gradient(to top, var(--view-theme), #667eea);
	border-radius: 4rpx 4rpx 0 0;
	margin-bottom: 10rpx;
	min-height: 10rpx;
}

.bar-label {
	font-size: 20rpx;
	color: #666;
	transform: rotate(-45deg);
	white-space: nowrap;
}

.sales-overview {
	.sales-grid {
		display: flex;
		flex-wrap: wrap;
	}

	.sales-item {
		width: 50%;
		text-align: center;
		margin-bottom: 30rpx;
	}

	.sales-number {
		font-size: 48rpx;
		font-weight: bold;
		color: var(--view-theme);
		margin-bottom: 8rpx;
	}

	.sales-label {
		font-size: 26rpx;
		color: #666;
	}
}

.goods-list {
	.goods-item {
		display: flex;
		align-items: center;
		padding: 20rpx 0;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.goods-item:last-child {
		border-bottom: none;
	}

	.goods-rank {
		width: 60rpx;
		height: 60rpx;
		background: var(--view-theme);
		color: white;
		border-radius: 30rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		font-weight: bold;
		margin-right: 20rpx;
	}

	.goods-image {
		width: 80rpx;
		height: 80rpx;
		border-radius: 8rpx;
		overflow: hidden;
		margin-right: 20rpx;
	}

	.goods-image image {
		width: 100%;
		height: 100%;
	}

	.goods-info {
		flex: 1;
	}

	.goods-name {
		font-size: 28rpx;
		color: #333;
		margin-bottom: 8rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.goods-sales {
		font-size: 24rpx;
		color: #666;
	}

	.goods-revenue {
		font-size: 32rpx;
		font-weight: bold;
		color: var(--view-theme);
	}
}

.order-overview {
	display: flex;
	justify-content: space-around;
}

.order-item {
	text-align: center;
}

.order-count {
	font-size: 48rpx;
	font-weight: bold;
	color: var(--view-theme);
	margin-bottom: 8rpx;
}

.order-label {
	font-size: 26rpx;
	color: #666;
}
</style>
