<template>
	<view class="merchant-center">
		<!-- 头部商家信息 -->
		<view class="merchant-header" :style="colorStyle">
			<view class="merchant-info">
				<view class="merchant-avatar">
					<image :src="merchantInfo.logo || '/static/images/default-merchant.png'" mode="aspectFill"></image>
				</view>
				<view class="merchant-details">
					<view class="merchant-name">{{ merchantInfo.name || '商家名称' }}</view>
					<view class="merchant-status">
						<text class="status-text" :class="merchantInfo.status === 1 ? 'active' : 'inactive'">
							{{ merchantInfo.status === 1 ? '营业中' : '休息中' }}
						</text>
					</view>
				</view>
				<view class="merchant-settings" @click="goSettings">
					<text class="iconfont icon-shezhi"></text>
				</view>
			</view>
			
			<!-- 余额显示 -->
			<view class="balance-info">
				<view class="balance-item">
					<view class="balance-amount">¥{{ merchantInfo.balance || '0.00' }}</view>
					<view class="balance-label">账户余额</view>
				</view>
				<view class="balance-item">
					<view class="balance-amount">{{ merchantInfo.todayRevenue || '0.00' }}</view>
					<view class="balance-label">今日营业额</view>
				</view>
			</view>
		</view>

		<!-- 快捷操作 -->
		<view class="quick-actions">
			<view class="action-item" @click="goGoodsList">
				<view class="action-icon">
					<text class="iconfont icon-shangpin"></text>
				</view>
				<view class="action-text">商品管理</view>
			</view>
			<view class="action-item" @click="goAddGoods">
				<view class="action-icon">
					<text class="iconfont icon-tianjia"></text>
				</view>
				<view class="action-text">添加商品</view>
			</view>
			<view class="action-item" @click="goOrderList">
				<view class="action-icon">
					<text class="iconfont icon-dingdan"></text>
				</view>
				<view class="action-text">订单管理</view>
			</view>
			<view class="action-item" @click="goStatistics">
				<view class="action-icon">
					<text class="iconfont icon-tongji"></text>
				</view>
				<view class="action-text">数据统计</view>
			</view>
		</view>

		<!-- 统计概览 -->
		<view class="statistics-overview">
			<view class="overview-title">经营概览</view>
			<view class="overview-grid">
				<view class="overview-item">
					<view class="overview-number">{{ overviewData.totalGoods || 0 }}</view>
					<view class="overview-label">商品总数</view>
				</view>
				<view class="overview-item">
					<view class="overview-number">{{ overviewData.onSaleGoods || 0 }}</view>
					<view class="overview-label">在售商品</view>
				</view>
				<view class="overview-item">
					<view class="overview-number">{{ overviewData.todayOrders || 0 }}</view>
					<view class="overview-label">今日订单</view>
				</view>
				<view class="overview-item">
					<view class="overview-number">{{ overviewData.monthOrders || 0 }}</view>
					<view class="overview-label">本月订单</view>
				</view>
			</view>
		</view>

		<!-- 最近订单 -->
		<view class="recent-orders">
			<view class="section-title">
				<text>最近订单</text>
				<text class="more-btn" @click="goOrderList">查看更多</text>
			</view>
			<view class="order-list">
				<view class="order-item" v-for="order in recentOrders" :key="order.id" @click="goOrderDetail(order.id)">
					<view class="order-info">
						<view class="order-number">订单号：{{ order.order_sn }}</view>
						<view class="order-time">{{ order.created_at }}</view>
					</view>
					<view class="order-amount">¥{{ order.total_amount }}</view>
					<view class="order-status" :class="getOrderStatusClass(order.status)">
						{{ getOrderStatusText(order.status) }}
					</view>
				</view>
			</view>
		</view>

		<!-- 返回会员中心 -->
		<view class="back-to-user" @click="backToUserCenter">
			<text class="iconfont icon-fanhui"></text>
			<text>返回会员中心</text>
		</view>
	</view>
</template>

<script>
import { getMerchantInfo, getMerchantOverview } from '@/api/merchant_manage.js';
import { mapGetters } from 'vuex';
import colors from '@/mixins/color';

export default {
	mixins: [colors],
	data() {
		return {
			merchantInfo: {},
			overviewData: {},
			recentOrders: []
		}
	},
	computed: {
		...mapGetters(['isLogin'])
	},
	onLoad() {
		if (!this.isLogin) {
			uni.navigateTo({
				url: '/pages/users/login/index'
			});
			return;
		}
		this.getMerchantData();
	},
	onShow() {
		this.getMerchantData();
	},
	methods: {
		// 获取商家数据
		async getMerchantData() {
			try {
				const [merchantRes, overviewRes] = await Promise.all([
					getMerchantInfo(),
					getMerchantOverview()
				]);
				
				this.merchantInfo = merchantRes.data;
				this.overviewData = overviewRes.data.overview;
				this.recentOrders = overviewRes.data.recentOrders || [];
			} catch (error) {
				this.$util.Tips({
					title: error.message || '获取数据失败'
				});
			}
		},

		// 跳转到商品列表
		goGoodsList() {
			uni.navigateTo({
				url: '/pages/merchant/goods/list'
			});
		},

		// 跳转到添加商品
		goAddGoods() {
			uni.navigateTo({
				url: '/pages/merchant/goods/add'
			});
		},

		// 跳转到订单列表
		goOrderList() {
			uni.navigateTo({
				url: '/pages/merchant/orders/list'
			});
		},

		// 跳转到数据统计
		goStatistics() {
			uni.navigateTo({
				url: '/pages/merchant/statistics/index'
			});
		},

		// 跳转到设置
		goSettings() {
			uni.navigateTo({
				url: '/pages/merchant/settings/index'
			});
		},

		// 跳转到订单详情
		goOrderDetail(orderId) {
			uni.navigateTo({
				url: `/pages/merchant/orders/detail?id=${orderId}`
			});
		},

		// 返回会员中心
		backToUserCenter() {
			uni.switchTab({
				url: '/pages/user/index'
			});
		},

		// 获取订单状态文本
		getOrderStatusText(status) {
			const statusMap = {
				0: '待付款',
				1: '待发货',
				2: '待收货',
				3: '已完成',
				4: '已取消',
				5: '退款中'
			};
			return statusMap[status] || '未知状态';
		},

		// 获取订单状态样式类
		getOrderStatusClass(status) {
			const classMap = {
				0: 'status-pending',
				1: 'status-processing',
				2: 'status-shipping',
				3: 'status-completed',
				4: 'status-cancelled',
				5: 'status-refunding'
			};
			return classMap[status] || '';
		}
	}
}
</script>

<style lang="scss" scoped>
.merchant-center {
	background-color: #f5f5f5;
	min-height: 100vh;
}

.merchant-header {
	background: linear-gradient(135deg, var(--view-theme) 0%, #667eea 100%);
	padding: 40rpx 30rpx 30rpx;
	color: white;
}

.merchant-info {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
}

.merchant-avatar {
	width: 120rpx;
	height: 120rpx;
	border-radius: 60rpx;
	overflow: hidden;
	margin-right: 20rpx;
	border: 3px solid rgba(255, 255, 255, 0.3);
}

.merchant-avatar image {
	width: 100%;
	height: 100%;
}

.merchant-details {
	flex: 1;
}

.merchant-name {
	font-size: 36rpx;
	font-weight: bold;
	margin-bottom: 10rpx;
}

.status-text {
	font-size: 24rpx;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	background: rgba(255, 255, 255, 0.2);
}

.status-text.active {
	background: #4CAF50;
}

.status-text.inactive {
	background: #FF9800;
}

.merchant-settings {
	padding: 10rpx;
}

.merchant-settings .iconfont {
	font-size: 40rpx;
}

.balance-info {
	display: flex;
	justify-content: space-around;
}

.balance-item {
	text-align: center;
}

.balance-amount {
	font-size: 48rpx;
	font-weight: bold;
	margin-bottom: 8rpx;
}

.balance-label {
	font-size: 24rpx;
	opacity: 0.8;
}

.quick-actions {
	display: flex;
	background: white;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 30rpx 0;
	box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.action-item {
	flex: 1;
	text-align: center;
}

.action-icon {
	width: 80rpx;
	height: 80rpx;
	background: linear-gradient(135deg, var(--view-theme) 0%, #667eea 100%);
	border-radius: 40rpx;
	margin: 0 auto 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.action-icon .iconfont {
	font-size: 40rpx;
	color: white;
}

.action-text {
	font-size: 26rpx;
	color: #333;
}

.statistics-overview {
	background: white;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.overview-title {
	font-size: 32rpx;
	font-weight: bold;
	margin-bottom: 30rpx;
	color: #333;
}

.overview-grid {
	display: flex;
	flex-wrap: wrap;
}

.overview-item {
	width: 50%;
	text-align: center;
	margin-bottom: 30rpx;
}

.overview-number {
	font-size: 48rpx;
	font-weight: bold;
	color: var(--view-theme);
	margin-bottom: 8rpx;
}

.overview-label {
	font-size: 26rpx;
	color: #666;
}

.recent-orders {
	background: white;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.section-title {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.more-btn {
	font-size: 26rpx;
	color: var(--view-theme);
	font-weight: normal;
}

.order-item {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.order-item:last-child {
	border-bottom: none;
}

.order-info {
	flex: 1;
}

.order-number {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 8rpx;
}

.order-time {
	font-size: 24rpx;
	color: #999;
}

.order-amount {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-right: 20rpx;
}

.order-status {
	font-size: 24rpx;
	padding: 8rpx 16rpx;
	border-radius: 12rpx;
	color: white;
}

.status-pending {
	background: #FF9800;
}

.status-processing {
	background: #2196F3;
}

.status-shipping {
	background: #9C27B0;
}

.status-completed {
	background: #4CAF50;
}

.status-cancelled {
	background: #F44336;
}

.status-refunding {
	background: #607D8B;
}

.back-to-user {
	display: flex;
	align-items: center;
	justify-content: center;
	background: white;
	margin: 20rpx;
	padding: 30rpx;
	border-radius: 16rpx;
	color: var(--view-theme);
	font-size: 28rpx;
	box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.back-to-user .iconfont {
	margin-right: 10rpx;
	font-size: 32rpx;
}
</style>
