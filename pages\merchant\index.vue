<template>
	<view class="merchant-center">
		<!-- 头部商家信息 -->
		<view class="merchant-header" :style="colorStyle">
			<view class="merchant-info">
				<view class="merchant-avatar">
					<image :src="merchantInfo.logo || '/static/images/default-merchant.png'" mode="aspectFill"></image>
				</view>
				<view class="merchant-details">
					<view class="merchant-name">{{ merchantInfo.name || '商家名称' }}</view>
					<view class="merchant-status">
						<text class="status-text" :class="merchantInfo.status === 1 ? 'active' : 'inactive'">
							{{ merchantInfo.status === 1 ? '营业中' : '休息中' }}
						</text>
					</view>
				</view>
				<view class="merchant-settings" @click="goSettings">
					<text class="iconfont icon-shezhi"></text>
				</view>
			</view>
			
			<!-- 余额显示 -->
			<view class="balance-info">
				<view class="balance-item">
					<view class="balance-amount">¥{{ merchantInfo.balance || '0.00' }}</view>
					<view class="balance-label">账户余额</view>
				</view>
				<view class="balance-item">
					<view class="balance-amount">{{ merchantInfo.todayRevenue || '0.00' }}</view>
					<view class="balance-label">今日营业额</view>
				</view>
			</view>
		</view>

		<!-- 快捷操作 -->
		<view class="quick-actions">
			<view class="action-item" @click="goGoodsList">
				<view class="action-icon">
					<text class="iconfont icon-shangpin"></text>
				</view>
				<view class="action-text">商品管理</view>
			</view>
			<view class="action-item" @click="goAddGoods">
				<view class="action-icon">
					<text class="iconfont icon-tianjia"></text>
				</view>
				<view class="action-text">添加商品</view>
			</view>
			<view class="action-item" @click="goOrderList">
				<view class="action-icon">
					<text class="iconfont icon-dingdan"></text>
				</view>
				<view class="action-text">订单管理</view>
			</view>
			<view class="action-item" @click="goStatistics">
				<view class="action-icon">
					<text class="iconfont icon-tongji"></text>
				</view>
				<view class="action-text">数据统计</view>
			</view>
		</view>

		<!-- 统计概览 -->
		<view class="statistics-overview">
			<view class="overview-title">经营概览</view>
			<view class="overview-grid">
				<view class="overview-item">
					<view class="overview-number">{{ overviewData.totalGoods || 0 }}</view>
					<view class="overview-label">商品总数</view>
				</view>
				<view class="overview-item">
					<view class="overview-number">{{ overviewData.onSaleGoods || 0 }}</view>
					<view class="overview-label">在售商品</view>
				</view>
				<view class="overview-item">
					<view class="overview-number">{{ overviewData.todayOrders || 0 }}</view>
					<view class="overview-label">今日订单</view>
				</view>
				<view class="overview-item">
					<view class="overview-number">{{ overviewData.monthOrders || 0 }}</view>
					<view class="overview-label">本月订单</view>
				</view>
			</view>
		</view>

		<!-- 最近订单 -->
		<view class="recent-orders">
			<view class="section-title">
				<text>最近订单</text>
				<text class="more-btn" @click="goOrderList">查看更多</text>
			</view>
			<view class="order-list">
				<view class="order-item" v-for="order in recentOrders" :key="order.id" @click="goOrderDetail(order.id)">
					<view class="order-info">
						<view class="order-number">订单号：{{ order.order_sn }}</view>
						<view class="order-time">{{ order.created_at }}</view>
					</view>
					<view class="order-amount">¥{{ order.total_amount }}</view>
					<view class="order-status" :class="getOrderStatusClass(order.status)">
						{{ getOrderStatusText(order.status) }}
					</view>
				</view>
			</view>
		</view>

		<!-- 返回会员中心 -->
		<view class="back-to-user" @click="backToUserCenter">
			<text class="iconfont icon-fanhui"></text>
			<text>返回会员中心</text>
		</view>
	</view>
</template>

<script>
import { getMerchantInfo, getMerchantOverview } from '@/api/merchant_manage.js';
import { mapGetters } from 'vuex';
import colors from '@/mixins/color';

export default {
	mixins: [colors],
	data() {
		return {
			merchantInfo: {},
			overviewData: {},
			recentOrders: []
		}
	},
	computed: {
		...mapGetters(['isLogin'])
	},
	onLoad() {
		if (!this.isLogin) {
			uni.navigateTo({
				url: '/pages/users/login/index'
			});
			return;
		}
		this.getMerchantData();
	},
	onShow() {
		this.getMerchantData();
	},
	methods: {
		// 获取商家数据
		async getMerchantData() {
			try {
				const [merchantRes, overviewRes] = await Promise.all([
					getMerchantInfo(),
					getMerchantOverview()
				]);
				
				this.merchantInfo = merchantRes.data;
				this.overviewData = overviewRes.data.overview;
				this.recentOrders = overviewRes.data.recentOrders || [];
			} catch (error) {
				this.$util.Tips({
					title: error.message || '获取数据失败'
				});
			}
		},

		// 跳转到商品列表
		goGoodsList() {
			uni.navigateTo({
				url: '/pages/merchant/goods/list'
			});
		},

		// 跳转到添加商品
		goAddGoods() {
			uni.navigateTo({
				url: '/pages/merchant/goods/add'
			});
		},

		// 跳转到订单列表
		goOrderList() {
			uni.navigateTo({
				url: '/pages/merchant/orders/list'
			});
		},

		// 跳转到数据统计
		goStatistics() {
			uni.navigateTo({
				url: '/pages/merchant/statistics/index'
			});
		},

		// 跳转到设置
		goSettings() {
			uni.navigateTo({
				url: '/pages/merchant/settings/index'
			});
		},

		// 跳转到订单详情
		goOrderDetail(orderId) {
			uni.navigateTo({
				url: `/pages/merchant/orders/detail?id=${orderId}`
			});
		},

		// 返回会员中心
		backToUserCenter() {
			uni.switchTab({
				url: '/pages/user/index'
			});
		},

		// 获取订单状态文本
		getOrderStatusText(status) {
			const statusMap = {
				0: '待付款',
				1: '待发货',
				2: '待收货',
				3: '已完成',
				4: '已取消',
				5: '退款中'
			};
			return statusMap[status] || '未知状态';
		},

		// 获取订单状态样式类
		getOrderStatusClass(status) {
			const classMap = {
				0: 'status-pending',
				1: 'status-processing',
				2: 'status-shipping',
				3: 'status-completed',
				4: 'status-cancelled',
				5: 'status-refunding'
			};
			return classMap[status] || '';
		}
	}
}
</script>

<style lang="scss" scoped>
.merchant-center {
	background: linear-gradient(180deg, #f8f9ff 0%, #f5f5f5 100%);
	min-height: 100vh;
}

.merchant-header {
	background: linear-gradient(135deg, var(--view-theme) 0%, #667eea 100%);
	padding: 40rpx 30rpx 40rpx;
	color: white;
	position: relative;
	overflow: hidden;

	&::before {
		content: '';
		position: absolute;
		top: -50%;
		right: -20%;
		width: 200rpx;
		height: 200rpx;
		background: rgba(255, 255, 255, 0.1);
		border-radius: 50%;
		transform: rotate(45deg);
	}

	&::after {
		content: '';
		position: absolute;
		bottom: -30%;
		left: -10%;
		width: 150rpx;
		height: 150rpx;
		background: rgba(255, 255, 255, 0.05);
		border-radius: 50%;
	}
}

.merchant-info {
	display: flex;
	align-items: center;
	margin-bottom: 40rpx;
	position: relative;
	z-index: 2;
}

.merchant-avatar {
	width: 130rpx;
	height: 130rpx;
	border-radius: 65rpx;
	overflow: hidden;
	margin-right: 24rpx;
	border: 4px solid rgba(255, 255, 255, 0.3);
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
	transition: transform 0.3s ease;

	&:active {
		transform: scale(0.95);
	}
}

.merchant-avatar image {
	width: 100%;
	height: 100%;
}

.merchant-details {
	flex: 1;
}

.merchant-name {
	font-size: 38rpx;
	font-weight: 600;
	margin-bottom: 12rpx;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.status-text {
	font-size: 24rpx;
	padding: 6rpx 16rpx;
	border-radius: 20rpx;
	background: rgba(255, 255, 255, 0.25);
	backdrop-filter: blur(10rpx);
	font-weight: 500;
	display: inline-block;
}

.status-text.active {
	background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
	box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);
}

.status-text.inactive {
	background: linear-gradient(135deg, #FF9800 0%, #f57c00 100%);
	box-shadow: 0 4rpx 12rpx rgba(255, 152, 0, 0.3);
}

.merchant-settings {
	padding: 12rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	backdrop-filter: blur(10rpx);
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.9);
		background: rgba(255, 255, 255, 0.3);
	}
}

.merchant-settings .iconfont {
	font-size: 44rpx;
}

.balance-info {
	display: flex;
	justify-content: space-around;
	position: relative;
	z-index: 2;
}

.balance-item {
	text-align: center;
	padding: 20rpx;
	border-radius: 16rpx;
	background: rgba(255, 255, 255, 0.15);
	backdrop-filter: blur(10rpx);
	min-width: 200rpx;
	transition: transform 0.3s ease;

	&:active {
		transform: translateY(-4rpx);
	}
}

.balance-amount {
	font-size: 52rpx;
	font-weight: 700;
	margin-bottom: 8rpx;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.balance-label {
	font-size: 26rpx;
	opacity: 0.9;
	font-weight: 400;
}

.quick-actions {
	display: flex;
	background: white;
	margin: 24rpx;
	border-radius: 24rpx;
	padding: 40rpx 20rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
}

.action-item {
	flex: 1;
	text-align: center;
	transition: transform 0.3s ease;

	&:active {
		transform: translateY(-8rpx);
	}
}

.action-icon {
	width: 88rpx;
	height: 88rpx;
	background: linear-gradient(135deg, var(--view-theme) 0%, #667eea 100%);
	border-radius: 44rpx;
	margin: 0 auto 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.25);
	position: relative;
	overflow: hidden;

	&::before {
		content: '';
		position: absolute;
		top: -50%;
		left: -50%;
		width: 200%;
		height: 200%;
		background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
		transform: rotate(45deg);
		transition: transform 0.6s ease;
	}

	&:active::before {
		transform: rotate(45deg) translateX(100%);
	}
}

.action-icon .iconfont {
	font-size: 44rpx;
	color: white;
	position: relative;
	z-index: 2;
}

.action-text {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.statistics-overview {
	background: white;
	margin: 24rpx;
	border-radius: 24rpx;
	padding: 40rpx 30rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
	position: relative;
	overflow: hidden;

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 6rpx;
		background: linear-gradient(90deg, var(--view-theme) 0%, #667eea 100%);
	}
}

.overview-title {
	font-size: 36rpx;
	font-weight: 600;
	margin-bottom: 40rpx;
	color: #333;
	position: relative;

	&::after {
		content: '';
		position: absolute;
		bottom: -12rpx;
		left: 0;
		width: 60rpx;
		height: 4rpx;
		background: linear-gradient(90deg, var(--view-theme) 0%, #667eea 100%);
		border-radius: 2rpx;
	}
}

.overview-grid {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
}

.overview-item {
	width: calc(50% - 10rpx);
	text-align: center;
	padding: 30rpx 20rpx;
	background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
	border-radius: 16rpx;
	border: 1rpx solid rgba(102, 126, 234, 0.1);
	transition: all 0.3s ease;

	&:active {
		transform: translateY(-4rpx);
		box-shadow: 0 12rpx 32rpx rgba(102, 126, 234, 0.15);
	}
}

.overview-number {
	font-size: 52rpx;
	font-weight: 700;
	background: linear-gradient(135deg, var(--view-theme) 0%, #667eea 100%);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
	margin-bottom: 12rpx;
}

.overview-label {
	font-size: 28rpx;
	color: #666;
	font-weight: 400;
}

.recent-orders {
	background: white;
	margin: 24rpx;
	border-radius: 24rpx;
	padding: 40rpx 30rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
}

.section-title {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 40rpx;
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
	position: relative;

	&::after {
		content: '';
		position: absolute;
		bottom: -12rpx;
		left: 0;
		width: 60rpx;
		height: 4rpx;
		background: linear-gradient(90deg, var(--view-theme) 0%, #667eea 100%);
		border-radius: 2rpx;
	}
}

.more-btn {
	font-size: 28rpx;
	color: var(--view-theme);
	font-weight: 500;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	background: rgba(102, 126, 234, 0.1);
	transition: all 0.3s ease;

	&:active {
		background: rgba(102, 126, 234, 0.2);
		transform: scale(0.95);
	}
}

.order-item {
	display: flex;
	align-items: center;
	padding: 24rpx 20rpx;
	margin-bottom: 16rpx;
	background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
	border-radius: 16rpx;
	border: 1rpx solid rgba(102, 126, 234, 0.1);
	transition: all 0.3s ease;

	&:last-child {
		margin-bottom: 0;
	}

	&:active {
		transform: translateX(8rpx);
		box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.15);
	}
}

.order-info {
	flex: 1;
}

.order-number {
	font-size: 30rpx;
	color: #333;
	margin-bottom: 8rpx;
	font-weight: 500;
}

.order-time {
	font-size: 26rpx;
	color: #999;
}

.order-amount {
	font-size: 34rpx;
	font-weight: 700;
	color: #333;
	margin-right: 24rpx;
}

.order-status {
	font-size: 24rpx;
	padding: 10rpx 20rpx;
	border-radius: 20rpx;
	color: white;
	font-weight: 500;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.status-pending {
	background: linear-gradient(135deg, #FF9800 0%, #f57c00 100%);
}

.status-processing {
	background: linear-gradient(135deg, #2196F3 0%, #1976d2 100%);
}

.status-shipping {
	background: linear-gradient(135deg, #9C27B0 0%, #7b1fa2 100%);
}

.status-completed {
	background: linear-gradient(135deg, #4CAF50 0%, #388e3c 100%);
}

.status-cancelled {
	background: linear-gradient(135deg, #F44336 0%, #d32f2f 100%);
}

.status-refunding {
	background: linear-gradient(135deg, #607D8B 0%, #455a64 100%);
}

.back-to-user {
	display: flex;
	align-items: center;
	justify-content: center;
	background: white;
	margin: 24rpx;
	padding: 40rpx;
	border-radius: 24rpx;
	color: var(--view-theme);
	font-size: 30rpx;
	font-weight: 500;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid rgba(102, 126, 234, 0.1);
	transition: all 0.3s ease;

	&:active {
		transform: translateY(-4rpx);
		box-shadow: 0 12rpx 40rpx rgba(102, 126, 234, 0.15);
		background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
	}
}

.back-to-user .iconfont {
	margin-right: 12rpx;
	font-size: 36rpx;
}
</style>
