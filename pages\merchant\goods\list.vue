<template>
	<view class="goods-list">
		<!-- 搜索栏 -->
		<view class="search-bar">
			<view class="search-input">
				<text class="iconfont icon-sousuo"></text>
				<input type="text" v-model="searchKeyword" placeholder="搜索商品名称" @confirm="searchGoods" />
			</view>
			<view class="search-btn" @click="searchGoods">搜索</view>
		</view>

		<!-- 筛选栏 -->
		<view class="filter-bar">
			<view class="filter-item" :class="{ active: filterStatus === '' }" @click="setFilter('')">
				全部
			</view>
			<view class="filter-item" :class="{ active: filterStatus === '1' }" @click="setFilter('1')">
				已上架
			</view>
			<view class="filter-item" :class="{ active: filterStatus === '0' }" @click="setFilter('0')">
				已下架
			</view>
		</view>

		<!-- 商品列表 -->
		<view class="goods-container">
			<view class="goods-item" v-for="goods in goodsList" :key="goods.id">
				<view class="goods-image">
					<image :src="goods.image" mode="aspectFill"></image>
					<view class="goods-status" :class="goods.status == 1 ? 'on-sale' : 'off-sale'">
						{{ goods.status == 1 ? '在售' : '下架' }}
					</view>
				</view>
				
				<view class="goods-info">
					<view class="goods-name">{{ goods.name }}</view>
					<view class="goods-price">
						<text class="current-price">¥{{ goods.price }}</text>
						<text class="original-price" v-if="goods.original_price">¥{{ goods.original_price }}</text>
					</view>
					<view class="goods-stats">
						<text>库存：{{ goods.stock }}</text>
						<text>销量：{{ goods.sales }}</text>
					</view>
				</view>

				<view class="goods-actions">
					<view class="action-btn edit-btn" @click="editGoods(goods.id)">
						<text class="iconfont icon-bianji"></text>
					</view>
					<view class="action-btn status-btn" @click="toggleStatus(goods)">
						<text class="iconfont" :class="goods.status == 1 ? 'icon-xiajia' : 'icon-shangjia'"></text>
					</view>
					<view class="action-btn delete-btn" @click="deleteGoods(goods.id)">
						<text class="iconfont icon-shanchu"></text>
					</view>
				</view>
			</view>
		</view>

		<!-- 空状态 -->
		<view class="empty-state" v-if="goodsList.length === 0 && !loading">
			<image src="/static/images/empty-goods.png" mode="aspectFit"></image>
			<text>暂无商品</text>
			<view class="add-goods-btn" @click="addGoods">
				<text class="iconfont icon-tianjia"></text>
				<text>添加商品</text>
			</view>
		</view>

		<!-- 加载更多 -->
		<view class="load-more" v-if="hasMore && goodsList.length > 0">
			<text v-if="loading">加载中...</text>
			<text v-else @click="loadMore">加载更多</text>
		</view>

		<!-- 悬浮添加按钮 -->
		<view class="floating-add" @click="addGoods">
			<text class="iconfont icon-tianjia"></text>
		</view>
	</view>
</template>

<script>
import { getMerchantGoods, updateGoodsStatus, deleteMerchantGoods } from '@/api/merchant_manage.js';

export default {
	data() {
		return {
			searchKeyword: '',
			filterStatus: '',
			goodsList: [],
			loading: false,
			hasMore: true,
			page: 1,
			limit: 10
		}
	},
	onLoad() {
		this.loadGoodsList();
	},
	onReachBottom() {
		if (this.hasMore && !this.loading) {
			this.loadMore();
		}
	},
	onPullDownRefresh() {
		this.refreshList();
	},
	methods: {
		// 加载商品列表
		async loadGoodsList(refresh = false) {
			if (this.loading) return;
			
			this.loading = true;
			
			if (refresh) {
				this.page = 1;
				this.goodsList = [];
			}

			try {
				const params = {
					page: this.page,
					limit: this.limit,
					keyword: this.searchKeyword,
					status: this.filterStatus
				};

				const res = await getMerchantGoods(params);
				const newList = res.data.list || [];
				
				if (refresh) {
					this.goodsList = newList;
				} else {
					this.goodsList.push(...newList);
				}
				
				this.hasMore = newList.length >= this.limit;
				this.page++;
			} catch (error) {
				this.$util.Tips({
					title: error.message || '获取商品列表失败'
				});
			} finally {
				this.loading = false;
				uni.stopPullDownRefresh();
			}
		},

		// 搜索商品
		searchGoods() {
			this.refreshList();
		},

		// 设置筛选条件
		setFilter(status) {
			this.filterStatus = status;
			this.refreshList();
		},

		// 刷新列表
		refreshList() {
			this.loadGoodsList(true);
		},

		// 加载更多
		loadMore() {
			this.loadGoodsList();
		},

		// 编辑商品
		editGoods(goodsId) {
			uni.navigateTo({
				url: `/pages/merchant/goods/edit?id=${goodsId}`
			});
		},

		// 切换商品状态
		async toggleStatus(goods) {
			try {
				const newStatus = goods.status == 1 ? 0 : 1;
				const statusText = newStatus == 1 ? '上架' : '下架';
				
				const res = await uni.showModal({
					title: '确认操作',
					content: `确定要${statusText}该商品吗？`
				});

				if (res.confirm) {
					await updateGoodsStatus(goods.id, newStatus);
					goods.status = newStatus;
					
					this.$util.Tips({
						title: `${statusText}成功`,
						icon: 'success'
					});
				}
			} catch (error) {
				this.$util.Tips({
					title: error.message || '操作失败'
				});
			}
		},

		// 删除商品
		async deleteGoods(goodsId) {
			try {
				const res = await uni.showModal({
					title: '确认删除',
					content: '删除后无法恢复，确定要删除该商品吗？'
				});

				if (res.confirm) {
					await deleteMerchantGoods(goodsId);
					
					// 从列表中移除
					this.goodsList = this.goodsList.filter(item => item.id !== goodsId);
					
					this.$util.Tips({
						title: '删除成功',
						icon: 'success'
					});
				}
			} catch (error) {
				this.$util.Tips({
					title: error.message || '删除失败'
				});
			}
		},

		// 添加商品
		addGoods() {
			uni.navigateTo({
				url: '/pages/merchant/goods/add'
			});
		}
	}
}
</script>

<style lang="scss" scoped>
.goods-list {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

.search-bar {
	display: flex;
	align-items: center;
	background: white;
	padding: 20rpx 30rpx;
	margin-bottom: 20rpx;
}

.search-input {
	flex: 1;
	display: flex;
	align-items: center;
	background: #f5f5f5;
	border-radius: 50rpx;
	padding: 16rpx 24rpx;
	margin-right: 20rpx;
}

.search-input .iconfont {
	color: #999;
	margin-right: 16rpx;
	font-size: 32rpx;
}

.search-input input {
	flex: 1;
	font-size: 28rpx;
}

.search-btn {
	background: var(--view-theme);
	color: white;
	padding: 16rpx 32rpx;
	border-radius: 50rpx;
	font-size: 28rpx;
}

.filter-bar {
	display: flex;
	background: white;
	padding: 0 30rpx;
	margin-bottom: 20rpx;
}

.filter-item {
	flex: 1;
	text-align: center;
	padding: 30rpx 0;
	font-size: 28rpx;
	color: #666;
	border-bottom: 3rpx solid transparent;
}

.filter-item.active {
	color: var(--view-theme);
	border-bottom-color: var(--view-theme);
}

.goods-container {
	padding: 0 30rpx;
}

.goods-item {
	display: flex;
	background: white;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.goods-image {
	position: relative;
	width: 160rpx;
	height: 160rpx;
	border-radius: 12rpx;
	overflow: hidden;
	margin-right: 20rpx;
}

.goods-image image {
	width: 100%;
	height: 100%;
}

.goods-status {
	position: absolute;
	top: 8rpx;
	right: 8rpx;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	font-size: 20rpx;
	color: white;
}

.goods-status.on-sale {
	background: #4CAF50;
}

.goods-status.off-sale {
	background: #F44336;
}

.goods-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.goods-name {
	font-size: 30rpx;
	color: #333;
	font-weight: bold;
	margin-bottom: 16rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.goods-price {
	margin-bottom: 16rpx;
}

.current-price {
	font-size: 32rpx;
	color: #e93323;
	font-weight: bold;
	margin-right: 16rpx;
}

.original-price {
	font-size: 24rpx;
	color: #999;
	text-decoration: line-through;
}

.goods-stats {
	font-size: 24rpx;
	color: #666;
}

.goods-stats text {
	margin-right: 30rpx;
}

.goods-actions {
	display: flex;
	flex-direction: column;
	justify-content: space-around;
	margin-left: 20rpx;
}

.action-btn {
	width: 60rpx;
	height: 60rpx;
	border-radius: 30rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.action-btn .iconfont {
	font-size: 32rpx;
	color: white;
}

.edit-btn {
	background: #2196F3;
}

.status-btn {
	background: #FF9800;
}

.delete-btn {
	background: #F44336;
}

.empty-state {
	text-align: center;
	padding: 100rpx 0;
	color: #999;
}

.empty-state image {
	width: 200rpx;
	height: 200rpx;
	margin-bottom: 30rpx;
}

.add-goods-btn {
	display: inline-flex;
	align-items: center;
	background: var(--view-theme);
	color: white;
	padding: 20rpx 40rpx;
	border-radius: 50rpx;
	margin-top: 30rpx;
	font-size: 28rpx;
}

.add-goods-btn .iconfont {
	margin-right: 10rpx;
	font-size: 32rpx;
}

.load-more {
	text-align: center;
	padding: 30rpx;
	color: #666;
	font-size: 28rpx;
}

.floating-add {
	position: fixed;
	right: 30rpx;
	bottom: 30rpx;
	width: 100rpx;
	height: 100rpx;
	background: var(--view-theme);
	border-radius: 50rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.2);
	z-index: 999;
}

.floating-add .iconfont {
	font-size: 48rpx;
	color: white;
}
</style>
