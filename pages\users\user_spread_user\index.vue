<template>
	<view :style="colorStyle">
		<view class='my-promotion'>
			<view class="header">
				<view class='name acea-row row-center-wrapper'>
					<!-- 当前佣金 -->
					<view>
						<view class="user-msg">
							<image class="avatar" :src="userInfo.avatar" mode=""></image>
							<view class="nickname line1">{{userInfo.nickname}}</view>
							<view v-if="userInfo.is_agent_level" class="level line1" @click="jumbPath">
								<text>{{userInfo.agent_level_name?$t(userInfo.agent_level_name):$t(`分销等级`)}}</text>
								<text v-if="userInfo.is_agent_level" class='iconfont icon-xiangyou'></text>
							</view>
						</view>
					</view>
				</view>
				<view class='num'>{{userInfo.brokerage_price}}</view>
				<view class='profit acea-row row-between-wrapper'>
					<view class='item'>
						<view>{{$t(`昨日收益`)}}</view>
						<view class='money'>{{userInfo.yesterDay}}</view>
					</view>
					<view class='item' @click="jumbPath(1)">
						<view>{{$t(`累积已提`)}}<text class='iconfont icon-xiangyou'></text></view>
						<view class='money'>{{userInfo.extractTotalPrice}}</view>
					</view>
				</view>
				<view class="apply"
					v-if="userInfo.division_open && userInfo.agent_apply_open && ((userInfo.is_division && userInfo.division_invite && userInfo.division_status) || (!userInfo.is_division && !userInfo.is_agent))">
					<view v-if="userInfo.is_division">{{$t(`邀请码`)}}：{{userInfo.division_invite}}</view>
					<view v-if="!userInfo.is_division && !userInfo.is_agent">
						<navigator url='/pages/annex/settled/index' hover-class="none">
							<view>{{$t(`代理商申请`)}}</view>
						</navigator>
					</view>
				</view>
			</view>
			<!-- #ifdef APP-PLUS || H5 -->
			<navigator url="/pages/users/user_cash/index" hover-class="none" class='bnt bg-color'>{{$t(`立即提现`)}}</navigator>
			<!-- #endif -->
			<!-- #ifdef MP -->
			<view @click="openSubscribe('/pages/users/user_cash/index')" class='bnt bg-color'>{{$t(`立即提现`)}}</view>
			<!-- #endif -->
			<view class='list acea-row row-between-wrapper'>
				<navigator url='/pages/users/user_spread_code/index' hover-class="none"
					class='item acea-row row-center-wrapper row-column'>
					<text class='iconfont icon-erweima'></text>
					<view>{{$t(`推广名片`)}}</view>
				</navigator>
				<navigator url='/pages/users/promoter-list/index' hover-class="none"
					class='item acea-row row-center-wrapper row-column'>
					<text class='iconfont icon-tongji'></text>
					<view>{{$t(`团队人统计`)}}</view>
				</navigator>
				<navigator url='/pages/users/user_spread_money/index?type=2' hover-class="none"
					class='item acea-row row-center-wrapper row-column'>
					<text class='iconfont icon-qiandai'></text>
					<view>{{$t(`佣金明细`)}}</view>
				</navigator>

				<navigator  v-if="(userInfo.division_open && !userInfo.is_agent && !userInfo.is_division) || !userInfo.division_open" url='/pages/users/promoter-order/index' hover-class="none"
					class='item acea-row row-center-wrapper row-column'>
					<text class='iconfont icon-dingdan'></text>
					<view>{{$t(`团队人订单`)}}</view>
				</navigator>
				<navigator v-if="userInfo.division_open && (userInfo.is_agent || userInfo.is_division)" url='/pages/users/promoter-order/index?type=1' hover-class="none"
					class='item acea-row row-center-wrapper row-column'>
					<text class='iconfont icon-dingdan'></text>
					<view>{{userInfo.is_division?$t(`事业部`):$t(`agent`)}}{{$t(`推广订单`)}}</view>
				</navigator>
				<navigator url='/pages/users/promoter_rank/index' hover-class="none"
					class='item acea-row row-center-wrapper row-column'>
					<text class='iconfont icon-paihang1'></text>
					<view>{{$t(`团队人排行`)}}</view>
				</navigator>
				<!-- <navigator url='/pages/users/commission_rank/index' hover-class="none"
					class='item acea-row row-center-wrapper row-column'>
					<text class='iconfont icon-paihang'></text>
					<view>{{$t(`佣金排行`)}}</view>
				</navigator> -->
				<navigator v-if="userInfo.division_open && userInfo.is_agent" url='/pages/users/staff_list/index' hover-class="none"
					class='item acea-row row-center-wrapper row-column'>
					<text class='iconfont icon-tuandui'></text>
					<view>{{$t(`员工列表`)}}</view>
				</navigator>
				
			</view>
		</view>
		<!-- #ifdef MP -->
		<!-- <authorize @onLoadFun="onLoadFun" :isAuto="isAuto" :isShowAuth="isShowAuth" @authColse="authColse"></authorize> -->
		<!-- #endif -->
		<!-- #ifndef MP -->
		<home></home>
		<!-- #endif -->
	</view>
</template>

<script>
	import {
		getUserInfo
	} from '@/api/user.js';
	import {
		openExtrctSubscribe
	} from '@/utils/SubscribeMessage.js';
	import {
		toLogin
	} from '@/libs/login.js';
	import {
		mapGetters
	} from "vuex";
	// #ifdef MP
	import authorize from '@/components/Authorize';
	// #endif
	import home from '@/components/home';
	import colors from '@/mixins/color.js'
	export default {
		components: {
			// #ifdef MP
			authorize,
			// #endif
			home
		},
		mixins: [colors],
		data() {
			return {
				userInfo: {},
				yesterdayPrice: 0.00,
				isAuto: false, //没有授权的不会自动授权
				isShowAuth: false //是否隐藏授权
			};
		},
		computed: mapGetters(['isLogin']),
		watch: {
			isLogin: {
				handler: function(newV, oldV) {
					if (newV) {
						this.getUserInfo();
					}
				},
				deep: true
			}
		},
		onLoad() {
			if (this.isLogin) {
				// try {
				// 	const user_spread_user = uni.getStorageSync('user_spread_user');
				// 	if (user_spread_user) {
				// 		this.getUserInfo();
				// 	} else {
				// 		uni.redirectTo({
				// 			url: '/pages/user/index'
				// 		});
				// 	}
				// } catch (e) {
				// 	uni.showToast({
				// 		title: e,
				// 		icon: 'none'
				// 	});
				// }
				this.getUserInfo()
			} else {
				toLogin();
			}
		},
		methods: {
			onLoadFun: function() {
				this.getUserInfo();
			},
			//跳转
			jumbPath(type) {
				if (type == 1) {
					uni.navigateTo({
						// 提现记录
						url: '/pages/users/user_spread_money/index?type=1'
					})
				} else {
					uni.navigateTo({
						// 分佣等级
						url: '/pages/users/user_distribution_level/index'
					})
				}
			},
			// 授权关闭
			authColse: function(e) {
				this.isShowAuth = e
			},
			openSubscribe: function(page) {
				uni.showLoading({
					title: this.$t(`正在加载`),
				})
				openExtrctSubscribe().then(res => {
					uni.hideLoading();
					uni.navigateTo({
						url: page,
					});
				}).catch(() => {
					uni.hideLoading();
				});
			},
			/**
			 * 获取个人用户信息
			 */
			getUserInfo: function() {
				let that = this;
				getUserInfo().then(res => {
					that.$set(that, 'userInfo', res.data);
					if (!res.data.spread_status) {
						that.$util.Tips({
							title: that.$t(`您目前暂无推广权限`)
						}, {
							tab: 1,
							url: '/pages/index/index'
						});
					}
				});
			}
		}
	}
</script>

<style scoped lang="scss">
	.my-promotion .header {
		// background-image: url("data:image/png;base64,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");
		background-image: url("data:image/png;base64,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******************************************************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****************************************+pP8UCM7zZQKxjag+GMeTAjUDL5uCBsuW4lPrWpHS7CIpU2TCxOc1cCibdejBJ1ZCPMMBCIClRlmNFclCH9yNTcoCkr6JL6UTeuSRds+i5sp+SjPQuCJBAoUZzEeHc61J7/dVdxebnFna8UcH+qt2nYSW7SSv9n7hwrubsJYsi2ksHvtmP5NfuZrpujWrouzRFhvgZ2YzlsrwfsQeBKBRoHba8iErFZLZsWdOthbhQHcZKyUeQ4CAYECi1bMzfuwa3+Wda3X/Q25/pkJ3wW3wOImw6ifEDiD4G7VSh3ZdXH6x5I6E1RyZM/x7csTMLjBnaGN8cG+JACfjfkMPUgdLkmgIR5lr/bOMsIUBLdyceNevXSh7xwuuDutuza5Jf/0I5G2TLqZFu7IhvBtCFyVQIO/+x5NnvmnG/xlfb1I87ZHD/GNWxMw+oofwUAafR3Mw/TRVmu96oub7RG9dvJvnkJwd8K7TFXKNHOaOsXBqK6eJZOOmxBPPgGo3nUINJjFt2zkzKJWoE1TvUgTu2XvUPYjCHS4FGkLTimhXS69UlJaXHvRtm/RKzco80xCslJEnkl4n/mdFtZP/rU6aX8CYG4wXmgCBA4lcDLhPaVp//vr9ZJvu+WHb7uFEs9AwEDgZML7TGh3ir+SmD4O9YZ+f+IjpxLc3cAuOZFu0WcStKVF08Usn58LkrOelFWGTFyKXucHAQh0JFB4gO745T+XgzhrjVu7SqxxBK1v0TOU+XgCJ/B5TyrtCjNkERv3+JGcB3A6wd1XtdDc3KuLtZlKizYRuAuFdtUFF5lePUI5EEgQcNo1udZtnQ0i/rrWBq0RH59VJ7QrtZs07ZY1VQd7WeOyV6rT6RCAQD2BcRx1iP6ncT7Wf2j6Zk7ppzVBdbHeH4OLTK8euWk5lk3msKa7bBLaDLfenP3tqhK4J64tTrunSWfxSRMrcq4eNmL48NMIuKBVuadY52ctIgnq0oL9HBdQETjLwb62F3gPAkYC7nBfIjAbS04+ljvQl94xgyWupRce8u6pBfdA+66Tqjbn2tsMc93pc7lqI51o0AItmia+9Te72MH6Is9BAAJ1BNwGLY23/noL8FoXdBifuM4Fa5PWB33X+kNot5LiOQh0IOAUgJIfpADsLfNoz9eBfuYWW2GpT/rFd0BAETcj0HsQb4rHab8lxLdepiITtW5V1IY8Cx51goA0/SUbMmndNu19CofAMgE3b7U5a97qkF+7vmlN8AL7TMPuDvU1sTgI7QxiCBxEwAnSkh1aD/jeQi/5Ienu5oLodViwrkFabxQXh/vcQePjSp+1DqrTtckJ8f7GVL9Jp9rjN2F/i6IE7Jl2PdCgaWJLaC/R3OEec7oRQoWeTCAQ4rU26E/zOTentR7o771GDMOgQ332N46jP9SXrJ+zDDRP7h/aDoEjCQRCvF8fNJdz8oNkiFB+yArXTrsvK1yJdwAZ6I4cDBf8dsnGc/rmuc06bNNoTcfoDgLakEsmnCa0tGhJrdzpgVFBCDyIgFsf1OLPGrF0iI/RVPrTJwPeH4SdpkLgEgQC+UHrg/b2EvlBSgHJD9YAVM9ESgId6kkbfYlRco5K3kpwL0UaaOVq/Od1SpZpK6u9L60Pz0MAAuciEPjPl5i91Yi3OX0Yhh/O1SJqAwEI9CLgFH5yvSkV2CU3SOm3aN3rVU/KuReBxwnugbCuiVYTrMKEu9ccoDUQmBBwZnStDX6NKCWkQDVtyvirlpLjeQicnEDgpqv1ocSl1h/oFeiejK87edOp3kkI3Fpwdxuw928N/1uDX5uxJhsn5Bp6vAOBExJwPqmhD7z/v0tr6zNUKcMEAnspPZ6HwAkJuHzwEs4lK/mYuhq56R3sjsB+wk6+YJVqBuClmuk2Zq858xPQ0gYf1JrNPmMphGcgAIFzEwiyVfmN2VphL6zrUC+3GPxUreR4DgIXIBC4ymltKM1UpQO8Xxs4zF+gv69SxdsL7nFHRBo2/bNn8A5GcdklfkdrdpUhTD0h0I+A26i91t1nmvDBavrQJ0sV8S39uFMSBK5AILLie9nhE8zq14dUXvcrtI86XoPA4wT3a3QLtYQABCAAAQhAAAIQgMCUAII7IwICEIAABCAAAQhAAAIXIIDgfoFOoooQgAAEIAABCEAAAhBAcGcMQAACEIAABCAAAQhA4AIEENwv0ElUEQIQgAAEIAABCEAAAgjujAEIQAACEIAABCAAAQhcgACC+wU6iSpCAAIQgAAEIAABCEAAwZ0xAAEIQAACEIAABCAAgQsQQHC/QCdRRQhAAAIQgAAEIAABCCC4MwYgAAEIQAACEIAABCBwAQII7hfoJKoIAQhAAAIQgAAEIAABBHfGAAQgAAEIQAACEIAABC5AAMH9Ap1EFSEAAQhAAAIQgAAEIIDgzhiAAAQgAAEIQAACEIDABQgguF+gk6giBCAAAQhAAAIQgAAEENwZAxCAAAQgAAEIQAACELgAAQT3C3QSVYQABCAAAQhAAAIQgACCO2MAAhCAAAQgAAEIQAACFyCA4H6BTqKKEIAABCAAAQhAAAIQQHBnDEAAAhCAAAQgAAEIQOACBBDcL9BJVBECEIAABCAAAQhAAAII7owBCEAAAhCAAAQgAAEIXIAAgvsFOokqQgACEIAABCAAAQhAAMGdMQABCEAAAhCAAAQgAIELEEBwv0AnUUUIQAACEIAABCAAAQgguDMGIAABCEAAAhCAAAQgcAECCO4X6CSqCAEIQAACEIAABCAAAQR3xgAEIAABCEAAAhCAAAQuQADB/QKdRBUhAAEIQAACEIAABCCA4M4YgAAEIAABCEAAAhCAwAUIILhfoJOoIgQgAAEIQAACEIAABBDcGQMQgAAEIAABCEAAAhC4AAEE9wt0ElWEAAQgAAEIQAACEIAAgjtjAAIQgAAEIAABCEAAAhcggOB+gU6iihCAAAQgAAEIQAACEEBwZwxAAAIQgAAEIAABCEDgAgQQ3C/QSVQRAhCAAAQgAAEIQAACCO6MAQhAAAIQgAAEIAABCFyAAIL7BTqJKkIAAhCAAAQgAAEIQADBnTEAAQhAAAIQgAAEIACBCxBAcL9AJ125iuM4aoz9JfgLmzO+Xq/f9TcMg/7LDwIQeBgBt0Z84dYItT7cl/wa8dswDPq/+UEAAg8iMI6j5AetD1oXYpn1LT84GeIx6wOC+4MmwF5NHcfxy9fr5f806SzjTJPuN/f3yzAM+r/5QQACNyPgBPV4jbC0UmvEr8EawWHfQo1nIHAhAuM4Skj364MX2NdaoLXBrw9aI36980HfIlCtAePfIfByp+K/vV6vvwaasxYyEtx/eb1eP995ArYA4l0IXImA25D/7jblHnuPNmgd8n++EgfqCgEIzAmM4yj5QX8S1lt/EuK9/HA7JWCPxbMVMO9fmIAT2P/hBPYtWqIJqI35JwT4LfBSJgS2JeAEdq0R0qJt8ZPmXesDAvwWdCkTAhsScAK7DvSyzvf+eS281ofbCPAI7r2HyYPKG8dRm7Em3B4/Nuc9KPMNCHQi4FxitEZIi7bHTxr4H++0Qe8BjW9A4AgCzqX2nxsJ7HGT3grAYRh+PKKtvb+J4N6b6APKc1r2f3UyaZUSk/nrB7Tvpdh4HgL7EdhYi7bUkFtt0Pv1GF+CwD4EDjjQhw2TAvD7qx/uEdz3Gau3+co4jvJh1yn5yLGjyfcdmWhuM6xoyI0I7GyJy5GT9l0b9GMyTdxoCNGUmxJwSr9/76Rlz1HUmiDL3GVd644Uvm46NO/bLKdFk9Be+/ObaI9xp7KkeZcGnh8EIHACAuM4an1ocY3RvO6xPoiGfFp1wEd4P8HYoArPJtBBaO8pP6gzJLz/dMVe6bVAXrHt1LmAgBPa5a9aM2Y+5ilnJpObTY9ANU1kbcy3CTop6BIehcCpCDQK7R8XOBfMqjWiR7Ca1p5vEd5PNVSozMMIOKH9P5Xyw0RJN46j4uoki/T4XVJ4rxHCesCijAsRcEEkMm/V/rRxfoRrJ7xrEvfYmGfCuyt/wJWmtrt4DwJlBBqFds3hb0Lh2gnvWiN6/GaadydIjAj0PfBSBgTyBDpo2pURZhJU2rjexJWdCe9af86sEERwZ8YtEnAbqIT22rGizfHr+COdJ95Mq+b8bPVZ0kgyxiGwIYEOLnTKxf59Yo34v06HexWtC1m+C78xjqPWNfLAbzg2KBoCbp61WNhlVVfMyufXQZkYd8zkG658afbljnu6i95qhTFG40MIjOPYunn+PgzDN4lNuae5S8XPNn9Xd2nzFKR2usn3kCFEM29MwGnTtEa0/JSm7YfEGiF3GQXD9/ppE/4EpAVKCbnpSOuGL3wv0pQDAV1l2h7zIo4Ti73+B2dV/29HyCnln+J1tP6cLpYOwb1jz9+tqE7ZIXIadwWwtQS6pnDHG7MmnTZ/fOHvNjhpzykIjOMod5bWmw73Ety1DkgI+BziA8GCQNZTjCgqcRcCHbXiewjuM+WfOxxIKSE5+VS+8Ajud5klndvRSZP2PhxnXGW8UN2z5ilfWS9YkIWmJ2nKejyBDi4ynuHMjcVp1ba44G3yrShojjSzjx/VAOhFoIO13ldFFvNZ9rhxHKVx7y3Dxi4z4RqUVDD04lVSTu9Gl3ybZ09MYBzHnmbqr2MzdMfTeExxEsji8s6rLW9ZgCw0Jx50VO0yBDoHmP82DMO3ceM7WfxSTOPNOUxhSRaay4xCKnpWAh0P9WrixJLu27yR4D5x7Y207vr0LFD2iD5AcD+C+sm/2Tmjg1qrjBETH/PCb8iMrXyr+q8y0Sxdk5zSuocn85m5/OTdQfUgcDoCnVOy9YiDUfCa1gitMwqEW0pdOzkoJNYi3GZON+Ko0FUIdD7Uq9lJN5UCjb72fK0NXmuvtWEpdmai4U8oMQ93m0Fwv8ps2LGenbXtqnnKR00C+FpQ23vCxZckGBaGycRKBMgkNXw7IuZTELg0gYJN09LOljiY5C2IBotenKI29tU/jVncApBnIHAWApGVu0e1klpu4xqkA/0sM8yKjBMf7KUICNNhH265R3DvMaxuVMYG0dqik0rnpLG3FBUurVc2G8zKxIvNXQqei3NCszHfaNzSlP0IbLAxz6xkao3hO59Lm1KtXzGlT+Z/xoKQ9K3djzRfgsD1CHRI/xg3Ohe8vhQYnzzQ+4INrjzxwT7Orpe0Eu7VWwjue5G+yHcMm2VNS3I+al9lClv1IzNks4j9WFOBLLMDRU3jeAcCTyLQKcVbjKwkDkbuMFpTJrmdwwITvqnx9yZa/ozrHv7uTxrYtLWZgGHe1Xwjd8+DtOCp/PBJLXu0Pqylo45j5cI4GF/UqpxS01jLOwjuFkoPeqbBTUabnCaMxpQmUzi2cj5qsTAtLbuezW7IRk2cHosnXirYFpeZB41tmtqHQENQmOa3/mQBi1NIWuNgFrXsgUYttdHGAGJf1tTh/rDNuU9vUQoE9iNg0GQvVcb7oMfyQy7rVLynmzLHGW9yja32qSx4h8XLIbjvN6Yv8SWj39hMexXmR3YTQ3na9acxlvNR8xtl0pc9BcwtDEuBZ/612E8td8LGHH6JkUklz0CgMKg8rHKchlGbs+akBHitESl3ujAOZtH0HWnTrGkkLYf7wzbnM/Q3dYBACYEGa9zHKu+09l5+0BqQC14P57mUfdrLFy9RMwrtvskfK+CCC3HSGlDCrOZZBPcaajd9pyF3e84HTeNLm/NrGIYfZ9L+H7eymm42dXWTFq3k6uRw4qX83FUltO43Hc80qz+BBo1azl1O81JrhNaQ+FpzHwezavpWS92hQmuE9UIo6+EerXv/oUSJNyRgcGFN6uNSd724OS1N99+GYfguIT9IcJeAP0tgkVH6aZ3Rn1XutVjkDjnYWxtwwyFGkxITofZSpKpAT/nTpy5WSNRLk9OiZY9f/QSYrATd4uvOdICAgUAPjZrhM59HLGuEm9t+Ey8qPhQYFuJ7kllvSj7EsxB4AoFKN7qq+eUyR0kbP0k1nZAfdJAvOdD7ImKLXM6nfveDPYL7E2aTsY0NuZlNvmXGaoSbts/ZXqJlDz9jOTHr+aqDR2l7eB4CVyfQGAOj+Sgf924/t3kv3euw9i2LVU5lJC0Ga4Xz7xB4CoEGi/17D3bxbYuuLiUs3YHea9lLXvXPxpmncnEzVQePmgr5dxDcW+jd7N0ONxWagkst2NwhosSslSo2zucep3Ty7+w+8SwMeAYCZyNQaQr/zDN3CYo0VItasrV2u01ZG+nSRSprxejfQ6vc0t0Sh/iyWhrAMxA4A4GG+Bdffa0JWhskxDf9Ohzo9f3YlW4p4H1Xqz2Ce9PwuNfLFWZwnY412bThhWPpfYvhWnaYFD034WT2tvqpLnVCbOpayvs6uyTqXr1LayDQTqAieF1rhP60Pvg1Qv+/XyOKNfDOpUVrhNad1t9nw11xp+Nw30qa929NwHDpWar9/gAfzmX9bxLepfEu0sA3uM2l6maNgdG7u7rLILjfeiqVNa5CcA83PR8FHgrc2pQ1oH2ap8UKuQ1Zp9pe47JEcD/8GuOy3uJpCOxPoFBw1wasNeL3RKYIX3mtDZqnJgG+wZ0vB8squOt9Dvf7Dzm+eBECFXfAfFxRnLZeFvbQglYkwBtuVC8lGaeElIwj+WRVyC/9UOnzvQSk0u/y/AkJFLrK5C5F0MTzad58K2c5mnPNd6d2TZA4l2sNsRLBHVN4DWHeeRSBQsF95heeEeCLbiF0AkK8xtT2Q4ngzuG+ljLv3Z5AoeCetGAFAny4/5vjSwK/dp+KuoV7LLgvXto0DMP/Wj5W8i6Cewmtmz9bKLgvBnQ6AVwDXRP0+1J0bgJr8ukgUDtOrT7uql6R8FDaHp6HwB0IFPq4L96R4FJLao5rLSn2aw3WmNrgdXWJNfOUniWI/Q6DmDZsQqDQx31xv3WBrpIfNLc1R2tcZrS2tMTJxa4ya/dD7GaRqxWINul4Cj2WQKEZepf8524xUBqmmrE6OamvpKrCh/XY4cfXL0CgMKvMLoJuQ255EQ+zyiwFp+pZrHIXGKNU8RgCKzEiqUptLui6Okl+qImZm8x3gyvxbgGqNcLQMaOCr25OoCK4pDoIVY1xG67G4GIQSqElIORUYgafbOKbw+YDELgggYq5KB92Wb6qssi4zVK3ri7GyRRaAjz5yWHdsP7toqy44LCgyhB4EyjM496URSawyi9mqSq0BIQ9ac3j7t8xu/S0DhcE91aCN3q/IQ+rBHgJ36YgVDfBNfaUnlH/lRlMpnJNlJlJzHDSzfWCNUezf//z/I26laZAoBuBBu12URCqWyNkJpe2TL/3Ju+03qk1IpfqdantsSl8KfjsXYdhGL7pBpOCIHAzApUH6KIg1M+pexzD9IzZ9cVwIM/1QmyxX1tjdouBQXC/2cRpbU7hiTn+nDJDmPxVM0EkszRx7jChNI6lY7UkIhzBvXXg8P4jCDQc7j0fswDvviW/0jBQzR/ytc68tfgNhwnrBSu+7mjcHzHKaWQtgQYl23sqLynw4jplgtRnmewqDxP6XEn8i55H4147cHivjUChD2vuY9pQTQElC2nipMVXObXBqfGmvBZYoragcW8bPrz9AAKFmWVyRMz+4kGgWrgW+EO+yq/NQBXfrLymUUNwf8D4pon1BAozy+Q+VHQT+4IALyFevu01/u2x4i+0/pk09PUU198s1WKul8gTlybQaeIVb3BOgPepJHtcrFK6KROceumRS+X3ItCoVfPVLL6wxAnw/r6IHntXSWCq6i1f++/24sx3IHA1Am4f9y6wLdU3p5D2H+mUZcoXV6P4W8yi1QIjfrfH4tezPpR1AgKN7jJqQdMAbjB9e3oTjb/RvI//6gnGHlU4P4GGYK/PHusscrUBq9q3lOZNf7W/OGPEmn+7vrNLlpzaBvEeBM5AoIPV3myNS7XXrU//arxZeZIhxuhus3mWHN9eBPczjPST1aFRo7aWn/Vttlq7KbGxDjWnZbRpJxuHVOe8BBrdZdbugJDlTVa7RcG+sQ6xRU5BsGv54HcLPjtvz1MzCCwTaAgG9QVnBWCn0f9yLRFGo+dA7CazlibW13s3V1sEd2bhjEBDQKjKWtzcgpPrYirJirRzWW2ecYMvNt0zdCDwVAKNVrG1jVmmdv18IGtSgDfO61QXxdlktDFbAuB3y9P81HFFu+9BwKihXp2b8QPBXTPvRBgLWaYsPuk52HE2GUt83K4WewT3e8yT7q1o0Hhn3WQyJ/FkKsmGTTnWtlsncJN7T/cOoEAInJxAwxzNaqYSB/ZZpilh6Xwxm2VjVj3kd1t0g+PJu5DqQWATAg0a70U3mYQbbzKVZIP8ErvZSkbWoX4t7m5XNzoE902G7fULbdC6ZzXX4zgumaP9CVoTx191XApSm6q0eR8N3co3w/J3M3OVNornIXBGAg2bc1JzbQhse1vpHAsJ2zXZImJtu3VjLg64P2OfUScI7EWgUuuenWcrN7trz5eFTmuE1gWtDzW/WNtuiX3Rd3ZV/CG413TtQ95ZmSg5ChKeNYg1gT6/hk2+hPbETacgiA7/9hLKPAsBR6DgYBwy0yYr4X3iAtPgHlfSH3HQmZQElk0e//YSyjz7eAI9lX/uUG/RfLdwrz3U726NQ3Bv6eYHvFt5ahYZnX7lg6bNWSdg3XK25XibndQL6r7rafkBw4YmPoRAw+bsL1vxty37VLBbkovd6MLbm5e+O7PkbVlJyobAXQg0HMal+PMadLmp1FrYrCg1x3Wol+X//StQXDZlwbFWMHxuS0Gqpj68czICDRvzni2ZafAKgud2DSrZEwrfgsAeBArm2h7VyX0jdbBXyjgdGNZ+u2/MaxXi3yFwFQIFCrQjmxS7yFgzyajOu6WB9IAQ3I8cKhf5tgsq1SZ3xvEyu2XNHTbkT78WUKIewAR+kXFINc9LoEA7dUQjZjc5F6Ssm2nijmgA34TAVQns5ObSgmcWWFpw2Dgk9uWMglhLB/DuRgROrFWbnJTV/AK/29mGvhE+ioXA7Qk0ZHLYkk3KBG5N/6h6oW3fsnco+xEECpVpezKZxbcVuvcckiIWwX3PIXLxb7kA06191a2UtCFLWy4/+s+vUHjAt91Km+cgYCBQuOkZSmx6JCW0a8+TNc6SkQbf9ib8vAyByd6sA7PVEr4HutmhvMASp/odltQCwX2P4XGjbzjhXYEiFjeUrVqey1xjzRBx6KTbCgrlQuAMBJzbjObikfuLgsx0MI8z11j92oUSN7ozDCjqcBsCzm1Gc3DtluKt25xyj9FhXgcLy7q1eyaZEIilglsDpPyLEXBmL2nej5h8uQ3Zmm9VtNGkXWzMUd1rETj4gJ+8DGUcxxKh/TBt2rV6mtpCoJyAs4wrMHxvGTSn9JPQrvXBqpA81Fq/N7TyHuaN0xLYWbOmCafLnfwFLB8uFYFxh/ilnbYjqRgENiDgtGuyzulQvccv6T73PqmXCe3JPPN7NIBvQOApBJxbihSAVmG5FY1STErgntx+XHEL8663pKYajeDeOhQe/n6wOW91etYkUz5XCe0Ts7fbkC3XlYe9NAtmfXgX0nwIbErAbYyap1tZ6HxOeK0R8aZc4tP+XlLifM6bwqFwCDycgFO86XC/lQAvK73c3iaXQjr5Qd/V2mSVhU9hibNW9uFDi+avEXDuM5oEEuB7TEC/Get0mxLY9Q2ZtixBZr76+KyudST/DoGNCDgBXr7vEuB77D1aFxScrjViIrC7TbnU/I3QvlHfUywElgg4BaBkB8kQJXt6rljNZQnqWhtmArtbH6TtL7EGnibDVI/FkxEJgQkBZwLT5lwqxGsj1iTTqdbfqDijW+GiM8v1TpdBAALHEXDpZbVGlAjxmsd+jdAm+rnlMGyJEwJ0QNCmbN3jVK6scclN/jhSfBkCzyIQKAG1NkhBZ53DWh+0Jkh20PowO8wHB3oJ7SUHhNMI7WqDFcizRg6t7UbAbaKaIH4CaszpT5PK/2nT1EUGyYnmK+MOBKVXHyeDUbocYeMvAAADU0lEQVQ1kIIgAIEmAok1QmuFXwv8GqENWbccr60REtYltJdY/ZK+r02N4mUIQKALAWep03yOhXi/Fvi1YWaZTxzoS2NusrF1XRpXWQiCeyU4XtuPgMtQoQ251EdWJ29p0RY3+/1awpcgAIEtCDgNfo2ZXX7xP25RJ8qEAASOJxBo8EsscKq4DgIKZk1a9o5sGYL7kfT5dpaAm2ze561Ee+YnnPzZs+42oIcABK5NwFngtEbUBMZrM9ah/nSb8rV7hdpD4HgCgc+81gZZ/Etk3Wyw+/Et+6MGJY05S52px80IuEkm4VwTTH/et620pdqEpUFDYC8lx/MQODGBwJ0mXCNq9i/WiBP3M1WDQA0Bp+iTDCHZwcsQpUUtJsQoLWzL52sWvi3rQ9kPIhAEkZUGsYaUfLpIBY8QWPag8UNT70+gU7Yqmbx1mE9mqLo/RVoIgXsSCNxoS7XqIRDJDYsBrWejh+B+th55aH3cBu1Py3Egig9mFZ13IKvPLoGp+6EDhmY/joALUvPWOL9Rx3uYzzzj1wkFveMO87jRQoOfRMApAUP5IRbkw4QYH/nBkhTjjBwR3M/YK9QJAhCAAAQgAAEIQAACEQEEd4YEBCAAAQhAAAIQgAAELkAAwf0CnUQVIQABCEAAAhCAAAQggODOGIAABCAAAQhAAAIQgMAFCCC4X6CTqCIEIAABCEAAAhCAAAQQ3BkDEIAABCAAAQhAAAIQuAABBPcLdBJVhAAEIAABCEAAAhCAAII7YwACEIAABCAAAQhAAAIXIIDgfoFOoooQgAAEIAABCEAAAhBAcGcMQAACEIAABCAAAQhA4AIEENwv0ElUEQIQgAAEIAABCEAAAgjujAEIQAACEIAABCAAAQhcgACC+wU6iSpCAAIQgAAEIAABCEAAwZ0xAAEIQAACEIAABCAAgQsQQHC/QCdRRQhAAAIQgAAEIAABCCC4MwYgAAEIQAACEIAABCBwAQII7hfoJKoIAQhAAAIQgAAEIAABBHfGAAQgAAEIQAACEIAABC5AAMH9Ap1EFSEAAQhAAAIQgAAEIIDgzhiAAAQgAAEIQAACEIDABQgguF+gk6giBCAAAQhAAAIQgAAEENwZAxCAAAQgAAEIQAACELgAgf8HAN8l0E31KAkAAAAASUVORK5CYII=");
		background-repeat: no-repeat;
		background-size: 100% 100%;
		width: 100%;
		// height: 480rpx;
		padding-bottom: 20rpx;
		background-color: var(--view-theme);
	}

	.my-promotion .header .name {
		font-size: 30rpx;
		color: #fff;
		padding-top: 37rpx;
		position: relative;

		.distribution {
			height: 52rpx;
			background-color: #ccc;
			font-size: 24rpx;
			color: #fff;
			position: absolute;
			right: 0;
			border-radius: 30rpx 0 0 30rpx;
			padding: 0 5rpx 0 10rpx;

			&.on {
				background-color: #FFF9E3;
				color: #D16739;
			}

			.iconfont {
				font-size: 18rpx;
			}

			.icon-dengjitubiao {
				font-size: 32rpx;
				margin-right: 10rpx;
			}
		}

		.user-msg {
			display: flex;
			align-items: center;
			justify-content: center;
			flex-direction: column;

			.nickname {
				font-size: 32rpx;
				margin: 10rpx 0;
				max-width: 8rem;
			}

			.level {
				font-size: 18rpx;
				padding: 4rpx 10rpx;
				background: linear-gradient(135deg, var(--view-bntColor) 0%, var(--view-main-over) 100%);
				border-radius: 6rpx;
				transform: scale(0.9);
				display: flex;
				align-items: center;
				max-width: 10rem;
				.icon-xiangyou {
					transform: scale(0.7);
					font-size: 28rpx;
				}
			}

			image {
				width: 100rpx;
				height: 100rpx;
				border-radius: 50%;
			}
		}
	}

	.my-promotion .header .name .record {
		font-size: 26rpx;
		color: rgba(255, 255, 255, 0.8);
		position: absolute;
		right: 20rpx;
	}

	.my-promotion .header .name .record .iconfont {
		font-size: 25rpx;
		margin-left: 10rpx;
		vertical-align: 2rpx;
	}

	.my-promotion .header .num {
		text-align: center;
		color: #fff;
		margin-top: 8rpx;
		font-size: 90rpx;
		font-family: 'Guildford Pro';
	}

	.my-promotion .header .profit {
		padding: 0 20rpx;
		margin-top: 35rpx;
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.8);
	}

	.my-promotion .header .profit .item {
		min-width: 200rpx;
		text-align: center;
	}

	.my-promotion .header .profit .item .iconfont {
		font-size: 18rpx;
		color: #fff;
		margin-top: 5rpx;
	}

	.my-promotion .header .profit .item .money {
		font-size: 34rpx;
		color: #fff;
		margin-top: 5rpx;
	}

	.my-promotion .bnt {
		font-size: 28rpx;
		color: #fff;
		width: 258rpx;
		border-radius: 50rpx;
		text-align: center;
		line-height: 68rpx;
		margin: -32rpx auto 0 auto;

		border: 16rpx solid #f5f5f5;
	}

	.my-promotion .list {
		padding: 0 20rpx 50rpx 20rpx;
		margin-top: 10rpx;
	}

	.my-promotion .list .item {
		width: 345rpx;
		height: 240rpx;
		border-radius: 20rpx;
		background-color: #fff;
		margin-top: 20rpx;
		font-size: 30rpx;
		color: #666;
	}

	.my-promotion .list .item .iconfont {
		font-size: 70rpx;
		// background-image: linear-gradient(to right, #fc4d3d 0%, #e93323 100%);
		background-color: var(--view-theme);
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
		margin-bottom: 20rpx;
	}

	.apply {
		top: 52rpx;
		right: 0;
		position: absolute;
		width: max-content;
		height: 56rpx;
		padding: 0 14rpx;
		background-color: #fff1db;
		color: #a56a15;
		font-size: 22rpx;
		border-radius: 30rpx 0 0 30rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
</style>
