import request from "@/utils/request.js";

/**
 * 获取商家信息
 */
export function getMerchantInfo() {
	return request.get("merchant/info");
}

/**
 * 获取商家余额
 */
export function getMerchantBalance() {
	return request.get("merchant/balance");
}

/**
 * 获取商家商品列表
 * @param {Object} data 查询参数
 */
export function getMerchantGoods(data) {
	return request.get("merchant/goods/list", data);
}

/**
 * 添加商品
 * @param {Object} data 商品数据
 */
export function addMerchantGoods(data) {
	return request.post("merchant/goods/add", data);
}

/**
 * 编辑商品
 * @param {Number} id 商品ID
 * @param {Object} data 商品数据
 */
export function editMerchantGoods(id, data) {
	return request.put(`merchant/goods/edit/${id}`, data);
}

/**
 * 删除商品
 * @param {Number} id 商品ID
 */
export function deleteMerchantGoods(id) {
	return request.delete(`merchant/goods/delete/${id}`);
}

/**
 * 商品上架/下架
 * @param {Number} id 商品ID
 * @param {Number} status 状态 1-上架 0-下架
 */
export function updateGoodsStatus(id, status) {
	return request.post("merchant/goods/status", {
		id: id,
		status: status
	});
}

/**
 * 获取商品详情
 * @param {Number} id 商品ID
 */
export function getMerchantGoodsDetail(id) {
	return request.get(`merchant/goods/detail/${id}`);
}

/**
 * 获取商家统计数据
 * @param {Object} data 查询参数
 */
export function getMerchantStatistics(data) {
	return request.get("merchant/statistics", data);
}

/**
 * 获取营业额统计
 * @param {Object} data 查询参数
 */
export function getRevenueStatistics(data) {
	return request.get("merchant/statistics/revenue", data);
}

/**
 * 获取销售统计
 * @param {Object} data 查询参数
 */
export function getSalesStatistics(data) {
	return request.get("merchant/statistics/sales", data);
}

/**
 * 获取订单统计
 * @param {Object} data 查询参数
 */
export function getOrderStatistics(data) {
	return request.get("merchant/statistics/orders", data);
}

/**
 * 获取商品分类列表
 */
export function getGoodsCategories() {
	return request.get("merchant/goods/categories");
}

/**
 * 上传商品图片
 * @param {Object} data 图片数据
 */
export function uploadGoodsImage(data) {
	return request.post("merchant/goods/upload_image", data);
}

/**
 * 获取商家订单列表
 * @param {Object} data 查询参数
 */
export function getMerchantOrders(data) {
	return request.get("merchant/orders", data);
}

/**
 * 获取商家订单详情
 * @param {String} orderId 订单ID
 */
export function getMerchantOrderDetail(orderId) {
	return request.get(`merchant/orders/detail/${orderId}`);
}

/**
 * 发货
 * @param {Object} data 发货数据
 */
export function deliverOrder(data) {
	return request.post("merchant/orders/deliver", data);
}

/**
 * 获取商家概览数据
 */
export function getMerchantOverview() {
	return request.get("merchant/overview");
}
