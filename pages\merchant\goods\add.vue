<template>
	<view class="add-goods">
		<form @submit="submitForm">
			<!-- 商品图片 -->
			<view class="form-section">
				<view class="section-title">商品图片</view>
				<view class="image-upload">
					<view class="image-list">
						<view class="image-item" v-for="(image, index) in goodsForm.images" :key="index">
							<image :src="image" mode="aspectFill"></image>
							<view class="delete-btn" @click="removeImage(index)">
								<text class="iconfont icon-shanchu"></text>
							</view>
						</view>
						<view class="upload-btn" @click="uploadImage" v-if="goodsForm.images.length < 5">
							<text class="iconfont icon-tianjia"></text>
							<text>添加图片</text>
						</view>
					</view>
					<view class="upload-tip">最多可上传5张图片，第一张为主图</view>
				</view>
			</view>

			<!-- 基本信息 -->
			<view class="form-section">
				<view class="section-title">基本信息</view>
				<view class="form-item">
					<view class="label">商品名称</view>
					<input type="text" v-model="goodsForm.name" placeholder="请输入商品名称" maxlength="50" />
				</view>
				<view class="form-item">
					<view class="label">商品分类</view>
					<picker @change="onCategoryChange" :value="categoryIndex" :range="categoryList" range-key="name">
						<view class="picker-input">
							{{ goodsForm.category_name || '请选择商品分类' }}
							<text class="iconfont icon-xiangyou"></text>
						</view>
					</picker>
				</view>
				<view class="form-item">
					<view class="label">商品描述</view>
					<textarea v-model="goodsForm.description" placeholder="请输入商品描述" maxlength="200"></textarea>
				</view>
			</view>

			<!-- 价格库存 -->
			<view class="form-section">
				<view class="section-title">价格库存</view>
				<view class="form-item">
					<view class="label">销售价格</view>
					<input type="digit" v-model="goodsForm.price" placeholder="0.00" />
				</view>
				<view class="form-item">
					<view class="label">原价</view>
					<input type="digit" v-model="goodsForm.original_price" placeholder="0.00（选填）" />
				</view>
				<view class="form-item">
					<view class="label">库存数量</view>
					<input type="number" v-model="goodsForm.stock" placeholder="请输入库存数量" />
				</view>
			</view>

			<!-- 商品详情 -->
			<view class="form-section">
				<view class="section-title">商品详情</view>
				<view class="detail-editor">
					<textarea v-model="goodsForm.detail" placeholder="请输入商品详细描述" maxlength="1000"></textarea>
				</view>
			</view>

			<!-- 其他设置 -->
			<view class="form-section">
				<view class="section-title">其他设置</view>
				<view class="form-item">
					<view class="label">商品状态</view>
					<switch v-model="goodsForm.status" :checked="goodsForm.status" />
					<text class="switch-text">{{ goodsForm.status ? '立即上架' : '暂不上架' }}</text>
				</view>
				<view class="form-item">
					<view class="label">商品重量</view>
					<input type="digit" v-model="goodsForm.weight" placeholder="0.00（kg）" />
				</view>
			</view>

			<!-- 提交按钮 -->
			<view class="submit-section">
				<button class="submit-btn" @click="submitForm" :disabled="submitting">
					{{ submitting ? '提交中...' : '保存商品' }}
				</button>
			</view>
		</form>
	</view>
</template>

<script>
import { addMerchantGoods, getGoodsCategories, uploadGoodsImage } from '@/api/merchant_manage.js';

export default {
	data() {
		return {
			goodsForm: {
				name: '',
				category_id: '',
				category_name: '',
				description: '',
				price: '',
				original_price: '',
				stock: '',
				detail: '',
				status: true,
				weight: '',
				images: []
			},
			categoryList: [],
			categoryIndex: 0,
			submitting: false
		}
	},
	onLoad() {
		this.loadCategories();
	},
	methods: {
		// 加载商品分类
		async loadCategories() {
			try {
				const res = await getGoodsCategories();
				this.categoryList = res.data || [];
			} catch (error) {
				this.$util.Tips({
					title: '获取分类失败'
				});
			}
		},

		// 分类选择
		onCategoryChange(e) {
			const index = e.detail.value;
			this.categoryIndex = index;
			this.goodsForm.category_id = this.categoryList[index].id;
			this.goodsForm.category_name = this.categoryList[index].name;
		},

		// 上传图片
		uploadImage() {
			const that = this;
			uni.chooseImage({
				count: 5 - this.goodsForm.images.length,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					res.tempFilePaths.forEach(tempPath => {
						that.$util.uploadImgs('upload/image', tempPath, (uploadRes) => {
							if (uploadRes && uploadRes.data && uploadRes.data.url) {
								that.goodsForm.images.push(uploadRes.data.url);
							} else {
								that.$util.Tips({
									title: '图片上传失败，请重试'
								});
							}
						}, (error) => {
							that.$util.Tips({
								title: '图片上传失败，请重试'
							});
						});
					});
				},
				fail: (err) => {
					if (err.errMsg && !err.errMsg.includes('cancel')) {
						that.$util.Tips({
							title: '选择图片失败'
						});
					}
				}
			});
		},

		// 删除图片
		removeImage(index) {
			this.goodsForm.images.splice(index, 1);
		},

		// 表单验证
		validateForm() {
			if (!this.goodsForm.name.trim()) {
				this.$util.Tips({ title: '请输入商品名称' });
				return false;
			}
			if (!this.goodsForm.category_id) {
				this.$util.Tips({ title: '请选择商品分类' });
				return false;
			}
			if (!this.goodsForm.price || this.goodsForm.price <= 0) {
				this.$util.Tips({ title: '请输入正确的销售价格' });
				return false;
			}
			if (!this.goodsForm.stock || this.goodsForm.stock < 0) {
				this.$util.Tips({ title: '请输入正确的库存数量' });
				return false;
			}
			if (this.goodsForm.images.length === 0) {
				this.$util.Tips({ title: '请至少上传一张商品图片' });
				return false;
			}
			return true;
		},

		// 提交表单
		async submitForm() {
			if (!this.validateForm()) return;
			if (this.submitting) return;

			this.submitting = true;

			try {
				const formData = {
					...this.goodsForm,
					status: this.goodsForm.status ? 1 : 0,
					price: parseFloat(this.goodsForm.price),
					original_price: this.goodsForm.original_price ? parseFloat(this.goodsForm.original_price) : 0,
					stock: parseInt(this.goodsForm.stock),
					weight: this.goodsForm.weight ? parseFloat(this.goodsForm.weight) : 0
				};

				await addMerchantGoods(formData);

				this.$util.Tips({
					title: '商品添加成功',
					icon: 'success'
				});

				setTimeout(() => {
					uni.navigateBack();
				}, 1500);

			} catch (error) {
				this.$util.Tips({
					title: error.message || '添加失败，请重试'
				});
			} finally {
				this.submitting = false;
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.add-goods {
	background: linear-gradient(180deg, #f8f9ff 0%, #f5f5f5 100%);
	min-height: 100vh;
	padding-bottom: 140rpx;
}

.form-section {
	background: white;
	margin: 24rpx;
	padding: 40rpx 30rpx;
	border-radius: 24rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
	position: relative;
	overflow: hidden;

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 6rpx;
		background: linear-gradient(90deg, var(--view-theme) 0%, #667eea 100%);
	}
}

.section-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 40rpx;
	position: relative;

	&::after {
		content: '';
		position: absolute;
		bottom: -12rpx;
		left: 0;
		width: 60rpx;
		height: 4rpx;
		background: linear-gradient(90deg, var(--view-theme) 0%, #667eea 100%);
		border-radius: 2rpx;
	}
}

.image-upload {
	.image-list {
		display: flex;
		flex-wrap: wrap;
		gap: 24rpx;
	}

	.image-item {
		position: relative;
		width: 180rpx;
		height: 180rpx;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
		transition: transform 0.3s ease;

		&:active {
			transform: scale(0.95);
		}
	}

	.image-item image {
		width: 100%;
		height: 100%;
	}

	.delete-btn {
		position: absolute;
		top: 12rpx;
		right: 12rpx;
		width: 48rpx;
		height: 48rpx;
		background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
		border-radius: 24rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 4rpx 12rpx rgba(255, 71, 87, 0.3);
		transition: transform 0.3s ease;

		&:active {
			transform: scale(0.9);
		}
	}

	.delete-btn .iconfont {
		font-size: 28rpx;
		color: white;
	}

	.upload-btn {
		width: 180rpx;
		height: 180rpx;
		border: 3rpx dashed rgba(102, 126, 234, 0.3);
		border-radius: 16rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		color: var(--view-theme);
		font-size: 26rpx;
		background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
		transition: all 0.3s ease;

		&:active {
			transform: scale(0.95);
			border-color: var(--view-theme);
			background: rgba(102, 126, 234, 0.05);
		}
	}

	.upload-btn .iconfont {
		font-size: 56rpx;
		margin-bottom: 12rpx;
	}

	.upload-tip {
		font-size: 26rpx;
		color: #999;
		margin-top: 24rpx;
		padding: 16rpx 20rpx;
		background: rgba(102, 126, 234, 0.05);
		border-radius: 12rpx;
		border-left: 4rpx solid var(--view-theme);
	}
}

.form-item {
	display: flex;
	align-items: center;
	margin-bottom: 36rpx;
	position: relative;
}

.form-item:last-child {
	margin-bottom: 0;
}

.label {
	width: 180rpx;
	font-size: 30rpx;
	color: #333;
	margin-right: 24rpx;
	font-weight: 500;
}

.form-item input,
.form-item textarea {
	flex: 1;
	padding: 24rpx 20rpx;
	border: 2rpx solid #e8eaed;
	border-radius: 12rpx;
	font-size: 30rpx;
	background: #fafbfc;
	transition: all 0.3s ease;

	&:focus {
		border-color: var(--view-theme);
		background: white;
		box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
	}

	&::placeholder {
		color: #999;
		font-size: 28rpx;
	}
}

.form-item textarea {
	height: 160rpx;
	resize: none;
}

.picker-input {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 24rpx 20rpx;
	border: 2rpx solid #e8eaed;
	border-radius: 12rpx;
	font-size: 30rpx;
	color: #333;
	background: #fafbfc;
	transition: all 0.3s ease;

	&:active {
		border-color: var(--view-theme);
		background: white;
		box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
	}
}

.picker-input .iconfont {
	color: var(--view-theme);
	font-size: 28rpx;
}

.switch-text {
	margin-left: 24rpx;
	font-size: 30rpx;
	color: #333;
	font-weight: 500;
}

.detail-editor textarea {
	width: 100%;
	height: 240rpx;
	padding: 24rpx 20rpx;
	border: 2rpx solid #e8eaed;
	border-radius: 12rpx;
	font-size: 30rpx;
	box-sizing: border-box;
	background: #fafbfc;
	transition: all 0.3s ease;
	resize: none;

	&:focus {
		border-color: var(--view-theme);
		background: white;
		box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
	}

	&::placeholder {
		color: #999;
		font-size: 28rpx;
	}
}

.submit-section {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	padding: 30rpx;
	background: white;
	border-top: 1rpx solid #f0f0f0;
	box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.submit-btn {
	width: 100%;
	height: 96rpx;
	background: linear-gradient(135deg, var(--view-theme) 0%, #667eea 100%);
	color: white;
	border: none;
	border-radius: 48rpx;
	font-size: 34rpx;
	font-weight: 600;
	box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
	transition: all 0.3s ease;

	&:active {
		transform: translateY(2rpx);
		box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
	}
}

.submit-btn:disabled {
	background: linear-gradient(135deg, #ccc 0%, #bbb 100%);
	box-shadow: none;
	transform: none;
}
</style>
