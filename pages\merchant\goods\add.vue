<template>
	<view class="add-goods">
		<form @submit="submitForm">
			<!-- 商品图片 -->
			<view class="form-section">
				<view class="section-title">商品图片</view>
				<view class="image-upload">
					<view class="image-list">
						<view class="image-item" v-for="(image, index) in goodsForm.images" :key="index">
							<image :src="image" mode="aspectFill"></image>
							<view class="delete-btn" @click="removeImage(index)">
								<text class="iconfont icon-shanchu"></text>
							</view>
						</view>
						<view class="upload-btn" @click="uploadImage" v-if="goodsForm.images.length < 5">
							<text class="iconfont icon-tianjia"></text>
							<text>添加图片</text>
						</view>
					</view>
					<view class="upload-tip">最多可上传5张图片，第一张为主图</view>
				</view>
			</view>

			<!-- 基本信息 -->
			<view class="form-section">
				<view class="section-title">基本信息</view>
				<view class="form-item">
					<view class="label">商品名称</view>
					<input type="text" v-model="goodsForm.name" placeholder="请输入商品名称" maxlength="50" />
				</view>
				<view class="form-item">
					<view class="label">商品分类</view>
					<picker @change="onCategoryChange" :value="categoryIndex" :range="categoryList" range-key="name">
						<view class="picker-input">
							{{ goodsForm.category_name || '请选择商品分类' }}
							<text class="iconfont icon-xiangyou"></text>
						</view>
					</picker>
				</view>
				<view class="form-item">
					<view class="label">商品描述</view>
					<textarea v-model="goodsForm.description" placeholder="请输入商品描述" maxlength="200"></textarea>
				</view>
			</view>

			<!-- 价格库存 -->
			<view class="form-section">
				<view class="section-title">价格库存</view>
				<view class="form-item">
					<view class="label">销售价格</view>
					<input type="digit" v-model="goodsForm.price" placeholder="0.00" />
				</view>
				<view class="form-item">
					<view class="label">原价</view>
					<input type="digit" v-model="goodsForm.original_price" placeholder="0.00（选填）" />
				</view>
				<view class="form-item">
					<view class="label">库存数量</view>
					<input type="number" v-model="goodsForm.stock" placeholder="请输入库存数量" />
				</view>
			</view>

			<!-- 商品详情 -->
			<view class="form-section">
				<view class="section-title">商品详情</view>
				<view class="detail-editor">
					<textarea v-model="goodsForm.detail" placeholder="请输入商品详细描述" maxlength="1000"></textarea>
				</view>
			</view>

			<!-- 其他设置 -->
			<view class="form-section">
				<view class="section-title">其他设置</view>
				<view class="form-item">
					<view class="label">商品状态</view>
					<switch v-model="goodsForm.status" :checked="goodsForm.status" />
					<text class="switch-text">{{ goodsForm.status ? '立即上架' : '暂不上架' }}</text>
				</view>
				<view class="form-item">
					<view class="label">商品重量</view>
					<input type="digit" v-model="goodsForm.weight" placeholder="0.00（kg）" />
				</view>
			</view>

			<!-- 提交按钮 -->
			<view class="submit-section">
				<button class="submit-btn" @click="submitForm" :disabled="submitting">
					{{ submitting ? '提交中...' : '保存商品' }}
				</button>
			</view>
		</form>
	</view>
</template>

<script>
import { addMerchantGoods, getGoodsCategories, uploadGoodsImage } from '@/api/merchant_manage.js';

export default {
	data() {
		return {
			goodsForm: {
				name: '',
				category_id: '',
				category_name: '',
				description: '',
				price: '',
				original_price: '',
				stock: '',
				detail: '',
				status: true,
				weight: '',
				images: []
			},
			categoryList: [],
			categoryIndex: 0,
			submitting: false
		}
	},
	onLoad() {
		this.loadCategories();
	},
	methods: {
		// 加载商品分类
		async loadCategories() {
			try {
				const res = await getGoodsCategories();
				this.categoryList = res.data || [];
			} catch (error) {
				this.$util.Tips({
					title: '获取分类失败'
				});
			}
		},

		// 分类选择
		onCategoryChange(e) {
			const index = e.detail.value;
			this.categoryIndex = index;
			this.goodsForm.category_id = this.categoryList[index].id;
			this.goodsForm.category_name = this.categoryList[index].name;
		},

		// 上传图片
		uploadImage() {
			const that = this;
			uni.chooseImage({
				count: 5 - this.goodsForm.images.length,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					res.tempFilePaths.forEach(tempPath => {
						that.$util.uploadImgs('upload/image', tempPath, (uploadRes) => {
							if (uploadRes && uploadRes.data && uploadRes.data.url) {
								that.goodsForm.images.push(uploadRes.data.url);
							} else {
								that.$util.Tips({
									title: '图片上传失败，请重试'
								});
							}
						}, (error) => {
							that.$util.Tips({
								title: '图片上传失败，请重试'
							});
						});
					});
				},
				fail: (err) => {
					if (err.errMsg && !err.errMsg.includes('cancel')) {
						that.$util.Tips({
							title: '选择图片失败'
						});
					}
				}
			});
		},

		// 删除图片
		removeImage(index) {
			this.goodsForm.images.splice(index, 1);
		},

		// 表单验证
		validateForm() {
			if (!this.goodsForm.name.trim()) {
				this.$util.Tips({ title: '请输入商品名称' });
				return false;
			}
			if (!this.goodsForm.category_id) {
				this.$util.Tips({ title: '请选择商品分类' });
				return false;
			}
			if (!this.goodsForm.price || this.goodsForm.price <= 0) {
				this.$util.Tips({ title: '请输入正确的销售价格' });
				return false;
			}
			if (!this.goodsForm.stock || this.goodsForm.stock < 0) {
				this.$util.Tips({ title: '请输入正确的库存数量' });
				return false;
			}
			if (this.goodsForm.images.length === 0) {
				this.$util.Tips({ title: '请至少上传一张商品图片' });
				return false;
			}
			return true;
		},

		// 提交表单
		async submitForm() {
			if (!this.validateForm()) return;
			if (this.submitting) return;

			this.submitting = true;

			try {
				const formData = {
					...this.goodsForm,
					status: this.goodsForm.status ? 1 : 0,
					price: parseFloat(this.goodsForm.price),
					original_price: this.goodsForm.original_price ? parseFloat(this.goodsForm.original_price) : 0,
					stock: parseInt(this.goodsForm.stock),
					weight: this.goodsForm.weight ? parseFloat(this.goodsForm.weight) : 0
				};

				await addMerchantGoods(formData);

				this.$util.Tips({
					title: '商品添加成功',
					icon: 'success'
				});

				setTimeout(() => {
					uni.navigateBack();
				}, 1500);

			} catch (error) {
				this.$util.Tips({
					title: error.message || '添加失败，请重试'
				});
			} finally {
				this.submitting = false;
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.add-goods {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

.form-section {
	background: white;
	margin-bottom: 20rpx;
	padding: 30rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
	padding-bottom: 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.image-upload {
	.image-list {
		display: flex;
		flex-wrap: wrap;
		gap: 20rpx;
	}

	.image-item {
		position: relative;
		width: 160rpx;
		height: 160rpx;
		border-radius: 12rpx;
		overflow: hidden;
	}

	.image-item image {
		width: 100%;
		height: 100%;
	}

	.delete-btn {
		position: absolute;
		top: 8rpx;
		right: 8rpx;
		width: 40rpx;
		height: 40rpx;
		background: rgba(0, 0, 0, 0.6);
		border-radius: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.delete-btn .iconfont {
		font-size: 24rpx;
		color: white;
	}

	.upload-btn {
		width: 160rpx;
		height: 160rpx;
		border: 2rpx dashed #ddd;
		border-radius: 12rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		color: #999;
		font-size: 24rpx;
	}

	.upload-btn .iconfont {
		font-size: 48rpx;
		margin-bottom: 8rpx;
	}

	.upload-tip {
		font-size: 24rpx;
		color: #999;
		margin-top: 20rpx;
	}
}

.form-item {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
}

.form-item:last-child {
	margin-bottom: 0;
}

.label {
	width: 160rpx;
	font-size: 28rpx;
	color: #333;
	margin-right: 20rpx;
}

.form-item input,
.form-item textarea {
	flex: 1;
	padding: 20rpx;
	border: 1rpx solid #e0e0e0;
	border-radius: 8rpx;
	font-size: 28rpx;
}

.form-item textarea {
	height: 120rpx;
}

.picker-input {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx;
	border: 1rpx solid #e0e0e0;
	border-radius: 8rpx;
	font-size: 28rpx;
	color: #333;
}

.picker-input .iconfont {
	color: #999;
	font-size: 24rpx;
}

.switch-text {
	margin-left: 20rpx;
	font-size: 28rpx;
	color: #666;
}

.detail-editor textarea {
	width: 100%;
	height: 200rpx;
	padding: 20rpx;
	border: 1rpx solid #e0e0e0;
	border-radius: 8rpx;
	font-size: 28rpx;
	box-sizing: border-box;
}

.submit-section {
	padding: 30rpx;
}

.submit-btn {
	width: 100%;
	background: var(--view-theme);
	color: white;
	border: none;
	border-radius: 50rpx;
	padding: 30rpx 0;
	font-size: 32rpx;
	font-weight: bold;
}

.submit-btn:disabled {
	background: #ccc;
}
</style>
